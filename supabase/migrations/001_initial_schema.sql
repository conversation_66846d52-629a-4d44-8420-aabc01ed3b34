-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "vector";

-- Enable Row Level Security
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO postgres, anon, authenticated, service_role;

-- Users table (integrates with <PERSON><PERSON><PERSON> Auth)
CREATE TABLE public.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT UNIQUE NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    preferred_languages TEXT[] DEFAULT '{}',
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    total_lessons_completed INTEGER DEFAULT 0,
    total_points_earned INTEGER DEFAULT 0,
    profile_image_url TEXT,
    join_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_active_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Users can only see and edit their own data
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Languages table
CREATE TABLE public.languages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code TEXT UNIQUE NOT NULL, -- 'en', 'es', 'fr', etc.
    name TEXT NOT NULL,
    native_name TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Language levels table
CREATE TABLE public.language_levels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    language_id UUID REFERENCES public.languages(id) ON DELETE CASCADE,
    level_code TEXT NOT NULL, -- 'A1', 'A2', 'B1', etc.
    level_name TEXT NOT NULL,
    description TEXT,
    order_index INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Topics table
CREATE TABLE public.topics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    icon_name TEXT,
    color_hex TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lessons table
CREATE TABLE public.lessons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    content JSONB NOT NULL,
    language_id UUID REFERENCES public.languages(id) ON DELETE CASCADE,
    level_id UUID REFERENCES public.language_levels(id) ON DELETE CASCADE,
    topic_id UUID REFERENCES public.topics(id) ON DELETE CASCADE,
    difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level BETWEEN 1 AND 5),
    estimated_duration_minutes INTEGER DEFAULT 10,
    lesson_type TEXT DEFAULT 'conversation', -- 'conversation', 'grammar', 'vocabulary', etc.
    is_published BOOLEAN DEFAULT false,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on lessons
ALTER TABLE public.lessons ENABLE ROW LEVEL SECURITY;

-- Anyone can view published lessons
CREATE POLICY "Anyone can view published lessons" ON public.lessons
    FOR SELECT USING (is_published = true);

-- User progress table
CREATE TABLE public.user_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    lesson_id UUID REFERENCES public.lessons(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'mastered')),
    score INTEGER DEFAULT 0 CHECK (score BETWEEN 0 AND 100),
    time_spent_seconds INTEGER DEFAULT 0,
    attempts_count INTEGER DEFAULT 0,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, lesson_id)
);

-- Enable RLS on user_progress
ALTER TABLE public.user_progress ENABLE ROW LEVEL SECURITY;

-- Users can only see their own progress
CREATE POLICY "Users can view own progress" ON public.user_progress
    FOR ALL USING (auth.uid() = user_id);

-- Learning sessions table
CREATE TABLE public.learning_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    lesson_id UUID REFERENCES public.lessons(id) ON DELETE CASCADE,
    session_type TEXT DEFAULT 'practice', -- 'practice', 'review', 'test'
    start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_time TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER,
    interactions_data JSONB DEFAULT '{}',
    performance_metrics JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on learning_sessions
ALTER TABLE public.learning_sessions ENABLE ROW LEVEL SECURITY;

-- Users can only see their own sessions
CREATE POLICY "Users can view own sessions" ON public.learning_sessions
    FOR ALL USING (auth.uid() = user_id);

-- AI Agents table
CREATE TABLE public.ai_agents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    personality_traits JSONB DEFAULT '{}',
    language_id UUID REFERENCES public.languages(id) ON DELETE CASCADE,
    specializations TEXT[] DEFAULT '{}',
    system_prompt TEXT NOT NULL,
    avatar_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on ai_agents
ALTER TABLE public.ai_agents ENABLE ROW LEVEL SECURITY;

-- Anyone can view active agents
CREATE POLICY "Anyone can view active agents" ON public.ai_agents
    FOR SELECT USING (is_active = true);

-- Chat conversations table
CREATE TABLE public.chat_conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES public.ai_agents(id) ON DELETE CASCADE,
    lesson_id UUID REFERENCES public.lessons(id) ON DELETE CASCADE,
    title TEXT,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'archived')),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on chat_conversations
ALTER TABLE public.chat_conversations ENABLE ROW LEVEL SECURITY;

-- Users can only see their own conversations
CREATE POLICY "Users can view own conversations" ON public.chat_conversations
    FOR ALL USING (auth.uid() = user_id);

-- Chat messages table
CREATE TABLE public.chat_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID REFERENCES public.chat_conversations(id) ON DELETE CASCADE,
    sender_type TEXT NOT NULL CHECK (sender_type IN ('user', 'agent')),
    content TEXT NOT NULL,
    message_type TEXT DEFAULT 'text', -- 'text', 'audio', 'image', 'lesson_content'
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on chat_messages
ALTER TABLE public.chat_messages ENABLE ROW LEVEL SECURITY;

-- Users can only see messages from their conversations
CREATE POLICY "Users can view own messages" ON public.chat_messages
    FOR SELECT USING (
        conversation_id IN (
            SELECT id FROM public.chat_conversations WHERE user_id = auth.uid()
        )
    );

-- Users can insert messages to their conversations
CREATE POLICY "Users can insert own messages" ON public.chat_messages
    FOR INSERT WITH CHECK (
        conversation_id IN (
            SELECT id FROM public.chat_conversations WHERE user_id = auth.uid()
        )
    );

-- Content embeddings table (for vector search)
CREATE TABLE public.lesson_embeddings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lesson_id UUID REFERENCES public.lessons(id) ON DELETE CASCADE,
    content_text TEXT NOT NULL,
    embedding vector(1536), -- OpenAI embedding dimension
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for vector similarity search
CREATE INDEX ON public.lesson_embeddings USING ivfflat (embedding vector_cosine_ops);

-- Enable RLS on lesson_embeddings
ALTER TABLE public.lesson_embeddings ENABLE ROW LEVEL SECURITY;

-- Anyone can search embeddings for published lessons
CREATE POLICY "Anyone can search lesson embeddings" ON public.lesson_embeddings
    FOR SELECT USING (
        lesson_id IN (SELECT id FROM public.lessons WHERE is_published = true)
    );

-- Content cache table
CREATE TABLE public.content_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cache_key TEXT UNIQUE NOT NULL,
    content_data JSONB NOT NULL,
    content_type TEXT NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for cache key lookups
CREATE INDEX idx_content_cache_key ON public.content_cache(cache_key);
CREATE INDEX idx_content_cache_expires ON public.content_cache(expires_at);

-- Enable RLS on content_cache
ALTER TABLE public.content_cache ENABLE ROW LEVEL SECURITY;

-- Service role can manage cache
CREATE POLICY "Service role can manage cache" ON public.content_cache
    FOR ALL USING (auth.role() = 'service_role');

-- Functions to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_lessons_updated_at BEFORE UPDATE ON public.lessons
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_user_progress_updated_at BEFORE UPDATE ON public.user_progress
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_ai_agents_updated_at BEFORE UPDATE ON public.ai_agents
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_chat_conversations_updated_at BEFORE UPDATE ON public.chat_conversations
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_content_cache_updated_at BEFORE UPDATE ON public.content_cache
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Insert initial data
INSERT INTO public.languages (code, name, native_name) VALUES
('en', 'English', 'English'),
('es', 'Spanish', 'Español'),
('fr', 'French', 'Français'),
('de', 'German', 'Deutsch'),
('it', 'Italian', 'Italiano'),
('pt', 'Portuguese', 'Português'),
('ja', 'Japanese', '日本語'),
('ko', 'Korean', '한국어'),
('zh', 'Chinese', '中文'),
('ar', 'Arabic', 'العربية');

-- Insert language levels for each language
INSERT INTO public.language_levels (language_id, level_code, level_name, description, order_index)
SELECT 
    l.id,
    level_info.code,
    level_info.name,
    level_info.description,
    level_info.order_index
FROM public.languages l
CROSS JOIN (
    VALUES 
    ('A1', 'Beginner', 'Basic vocabulary and simple phrases', 1),
    ('A2', 'Elementary', 'Common expressions and everyday topics', 2),
    ('B1', 'Intermediate', 'Concrete and abstract topics', 3),
    ('B2', 'Upper-Intermediate', 'Complex texts and specialized topics', 4),
    ('C1', 'Advanced', 'Implicit meaning and subtle language nuances', 5),
    ('C2', 'Proficient', 'Native-like fluency and precision', 6)
) AS level_info(code, name, description, order_index);

-- Insert sample topics
INSERT INTO public.topics (name, description, icon_name, color_hex) VALUES
('Travel', 'Travel-related vocabulary and conversations', 'airplane', '#3B82F6'),
('Food & Dining', 'Restaurant, cooking, and food vocabulary', 'utensils', '#EF4444'),
('Business', 'Professional and workplace communication', 'briefcase', '#10B981'),
('Daily Life', 'Everyday conversations and activities', 'home', '#F59E0B'),
('Health & Fitness', 'Medical and wellness vocabulary', 'heart', '#EC4899'),
('Technology', 'Tech-related vocabulary and concepts', 'computer', '#8B5CF6'),
('Culture & Arts', 'Cultural topics and artistic expressions', 'palette', '#F97316'),
('Weather & Nature', 'Environmental and weather-related topics', 'sun', '#06B6D4');

-- Insert sample AI agents
INSERT INTO public.ai_agents (name, description, personality_traits, language_id, specializations, system_prompt, is_active)
SELECT 
    agent_info.name,
    agent_info.description,
    agent_info.personality_traits::jsonb,
    l.id,
    agent_info.specializations,
    agent_info.system_prompt,
    true
FROM public.languages l
CROSS JOIN (
    VALUES 
    (
        'Sofia', 
        'Friendly and encouraging language tutor',
        '{"personality": "warm", "teaching_style": "encouraging", "expertise": "conversation"}',
        ARRAY['conversation', 'pronunciation', 'everyday_language'],
        'You are Sofia, a warm and encouraging language tutor. Focus on building confidence through positive reinforcement and practical conversation skills.'
    ),
    (
        'Professor Chen', 
        'Formal and structured grammar expert',
        '{"personality": "formal", "teaching_style": "structured", "expertise": "grammar"}',
        ARRAY['grammar', 'writing', 'formal_language'],
        'You are Professor Chen, a formal and methodical language instructor specializing in grammar and proper language structure.'
    ),
    (
        'Alex', 
        'Casual and fun conversation partner',
        '{"personality": "casual", "teaching_style": "conversational", "expertise": "slang"}',
        ARRAY['slang', 'casual_conversation', 'cultural_context'],
        'You are Alex, a casual and fun conversation partner who helps with informal language, slang, and cultural context.'
    )
) AS agent_info(name, description, personality_traits, specializations, system_prompt)
WHERE l.code IN ('en', 'es', 'fr'); 