-- =====================================================
-- NIRA Simulation System Database Schema
-- Phase 1: Foundation Implementation
-- Date: December 24, 2024
-- =====================================================

-- Create simulation personas table
CREATE TABLE simulation_personas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    target_audience TEXT NOT NULL,
    cultural_context JSONB NOT NULL DEFAULT '{}',
    vocabulary_focus TEXT[] NOT NULL DEFAULT '{}',
    difficulty_range VARCHAR(20) NOT NULL DEFAULT 'beginner-intermediate',
    icon_name VARCHAR(50) NOT NULL DEFAULT 'person',
    color_theme VARCHAR(20) NOT NULL DEFAULT 'blue',
    learning_objectives TEXT[] NOT NULL DEFAULT '{}',
    typical_scenarios TEXT[] NOT NULL DEFAULT '{}',
    is_active BOOLEAN NOT NULL DEFAULT true,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create simulations table
CREATE TABLE simulations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    persona_id UUID NOT NULL REFERENCES simulation_personas(id) ON DELETE CASCADE,
    language_id UUID NOT NULL REFERENCES languages(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    scenario_context TEXT NOT NULL,
    difficulty_level VARCHAR(20) NOT NULL DEFAULT 'beginner',
    estimated_duration INTEGER NOT NULL DEFAULT 10, -- minutes
    learning_objectives TEXT[] NOT NULL DEFAULT '{}',
    cultural_notes TEXT,
    vocabulary_focus TEXT[] NOT NULL DEFAULT '{}',
    grammar_focus TEXT[] NOT NULL DEFAULT '{}',
    dialogue_structure JSONB NOT NULL DEFAULT '{}',
    assessment_criteria JSONB NOT NULL DEFAULT '{}',
    completion_requirements JSONB NOT NULL DEFAULT '{}',
    tags TEXT[] NOT NULL DEFAULT '{}',
    is_active BOOLEAN NOT NULL DEFAULT true,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(persona_id, language_id, title)
);

-- Create simulation dialogues table
CREATE TABLE simulation_dialogues (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    simulation_id UUID NOT NULL REFERENCES simulations(id) ON DELETE CASCADE,
    sequence_order INTEGER NOT NULL,
    speaker_role VARCHAR(50) NOT NULL, -- 'user', 'npc', 'narrator', 'system'
    speaker_name VARCHAR(100),
    dialogue_text TEXT NOT NULL,
    dialogue_translation TEXT, -- for learning support
    audio_url VARCHAR(500),
    response_options JSONB NOT NULL DEFAULT '[]', -- for branching dialogues
    cultural_context TEXT,
    vocabulary_highlights TEXT[] NOT NULL DEFAULT '{}',
    grammar_notes TEXT,
    difficulty_level VARCHAR(20) NOT NULL DEFAULT 'beginner',
    is_critical_path BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(simulation_id, sequence_order)
);

-- Create user simulation progress table
CREATE TABLE user_simulation_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    simulation_id UUID NOT NULL REFERENCES simulations(id) ON DELETE CASCADE,
    persona_id UUID NOT NULL REFERENCES simulation_personas(id) ON DELETE CASCADE,
    completion_status VARCHAR(20) NOT NULL DEFAULT 'not_started', -- 'not_started', 'in_progress', 'completed', 'mastered'
    performance_score DECIMAL(5,2) DEFAULT 0.00,
    cultural_competency_score DECIMAL(5,2) DEFAULT 0.00,
    vocabulary_score DECIMAL(5,2) DEFAULT 0.00,
    grammar_score DECIMAL(5,2) DEFAULT 0.00,
    fluency_score DECIMAL(5,2) DEFAULT 0.00,
    time_spent INTEGER DEFAULT 0, -- seconds
    attempts_count INTEGER DEFAULT 0,
    dialogue_choices JSONB NOT NULL DEFAULT '{}',
    feedback_received TEXT,
    mistakes_made JSONB NOT NULL DEFAULT '[]',
    strengths_identified JSONB NOT NULL DEFAULT '[]',
    areas_for_improvement JSONB NOT NULL DEFAULT '[]',
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, simulation_id)
);

-- Create persona preferences table
CREATE TABLE persona_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    persona_id UUID NOT NULL REFERENCES simulation_personas(id) ON DELETE CASCADE,
    preference_score DECIMAL(5,2) NOT NULL DEFAULT 0.00, -- AI-calculated preference (0-100)
    completion_rate DECIMAL(5,2) NOT NULL DEFAULT 0.00, -- percentage of simulations completed
    average_performance DECIMAL(5,2) NOT NULL DEFAULT 0.00, -- average score across simulations
    total_time_spent INTEGER NOT NULL DEFAULT 0, -- total seconds spent on this persona
    simulations_completed INTEGER NOT NULL DEFAULT 0,
    simulations_mastered INTEGER NOT NULL DEFAULT 0,
    last_accessed TIMESTAMP WITH TIME ZONE,
    recommended_next JSONB NOT NULL DEFAULT '{}', -- AI recommendations
    learning_goals TEXT[] NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, persona_id)
);

-- Create simulation assessments table
CREATE TABLE simulation_assessments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    simulation_id UUID NOT NULL REFERENCES simulations(id) ON DELETE CASCADE,
    assessment_type VARCHAR(50) NOT NULL, -- 'vocabulary', 'grammar', 'cultural', 'fluency', 'comprehension'
    question_text TEXT NOT NULL,
    question_type VARCHAR(50) NOT NULL, -- 'multiple_choice', 'fill_blank', 'speaking', 'cultural_scenario'
    correct_answers JSONB NOT NULL DEFAULT '[]',
    answer_options JSONB NOT NULL DEFAULT '[]',
    points_value INTEGER NOT NULL DEFAULT 1,
    difficulty_level VARCHAR(20) NOT NULL DEFAULT 'beginner',
    cultural_context TEXT,
    explanation TEXT,
    sort_order INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create simulation vocabulary table
CREATE TABLE simulation_vocabulary (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    simulation_id UUID NOT NULL REFERENCES simulations(id) ON DELETE CASCADE,
    vocabulary_id UUID REFERENCES vocabulary(id) ON DELETE SET NULL,
    word VARCHAR(200) NOT NULL,
    translation VARCHAR(200) NOT NULL,
    pronunciation VARCHAR(300),
    part_of_speech VARCHAR(50),
    context_usage TEXT NOT NULL,
    difficulty_level VARCHAR(20) NOT NULL DEFAULT 'beginner',
    frequency_in_simulation INTEGER NOT NULL DEFAULT 1,
    is_key_vocabulary BOOLEAN NOT NULL DEFAULT false,
    cultural_notes TEXT,
    example_sentences TEXT[] NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance optimization
CREATE INDEX idx_simulation_personas_active ON simulation_personas(is_active, sort_order);
CREATE INDEX idx_simulations_persona_language ON simulations(persona_id, language_id);
CREATE INDEX idx_simulations_difficulty ON simulations(difficulty_level);
CREATE INDEX idx_simulation_dialogues_simulation_order ON simulation_dialogues(simulation_id, sequence_order);
CREATE INDEX idx_user_simulation_progress_user ON user_simulation_progress(user_id);
CREATE INDEX idx_user_simulation_progress_status ON user_simulation_progress(completion_status);
CREATE INDEX idx_user_simulation_progress_performance ON user_simulation_progress(performance_score DESC);
CREATE INDEX idx_persona_preferences_user ON persona_preferences(user_id);
CREATE INDEX idx_simulation_assessments_simulation ON simulation_assessments(simulation_id, sort_order);
CREATE INDEX idx_simulation_vocabulary_simulation ON simulation_vocabulary(simulation_id);

-- Add RLS (Row Level Security) policies
ALTER TABLE simulation_personas ENABLE ROW LEVEL SECURITY;
ALTER TABLE simulations ENABLE ROW LEVEL SECURITY;
ALTER TABLE simulation_dialogues ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_simulation_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE persona_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE simulation_assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE simulation_vocabulary ENABLE ROW LEVEL SECURITY;

-- RLS Policies for public read access to simulation content
CREATE POLICY "Public read access to simulation personas" ON simulation_personas
    FOR SELECT USING (is_active = true);

CREATE POLICY "Public read access to simulations" ON simulations
    FOR SELECT USING (is_active = true);

CREATE POLICY "Public read access to simulation dialogues" ON simulation_dialogues
    FOR SELECT USING (true);

CREATE POLICY "Public read access to simulation assessments" ON simulation_assessments
    FOR SELECT USING (is_active = true);

CREATE POLICY "Public read access to simulation vocabulary" ON simulation_vocabulary
    FOR SELECT USING (true);

-- RLS Policies for user-specific data
CREATE POLICY "Users can manage their own simulation progress" ON user_simulation_progress
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own persona preferences" ON persona_preferences
    FOR ALL USING (auth.uid() = user_id);

-- Create functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_simulation_personas_updated_at BEFORE UPDATE ON simulation_personas
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_simulation_progress_updated_at BEFORE UPDATE ON user_simulation_progress
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_persona_preferences_updated_at BEFORE UPDATE ON persona_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert core simulation personas
INSERT INTO simulation_personas (
    name, display_name, description, target_audience, cultural_context, 
    vocabulary_focus, difficulty_range, icon_name, color_theme, 
    learning_objectives, typical_scenarios, sort_order
) VALUES 
(
    'traveler',
    'Traveler & Tourist',
    'Perfect for short-term visits and travel scenarios. Learn essential communication skills for navigating new places, interacting with locals, and handling common travel situations.',
    'Tourists, vacation travelers, short-term visitors, backpackers',
    '{"focus": "practical_communication", "formality": "casual_to_polite", "cultural_awareness": "basic_courtesy", "common_mistakes": ["over_formality", "literal_translations"], "key_concepts": ["politeness_levels", "tipping_culture", "personal_space"]}',
    ARRAY['transportation', 'accommodation', 'food_ordering', 'directions', 'shopping', 'emergencies', 'numbers', 'time', 'weather', 'basic_courtesy'],
    'beginner-intermediate',
    'airplane',
    'blue',
    ARRAY['Navigate transportation systems', 'Order food and drinks', 'Ask for directions and help', 'Handle accommodation check-in/out', 'Shop for essentials', 'Deal with emergencies', 'Understand cultural etiquette'],
    ARRAY['Airport and flight procedures', 'Hotel check-in and services', 'Restaurant ordering', 'Public transportation', 'Shopping and bargaining', 'Tourist attractions', 'Emergency situations'],
    1
),
(
    'living_abroad',
    'Living Abroad & Expat',
    'Designed for long-term residents and expats. Master deeper cultural integration, bureaucratic processes, and building meaningful relationships in your new home country.',
    'Expats, long-term residents, immigrants, international students',
    '{"focus": "cultural_integration", "formality": "varied_contexts", "cultural_awareness": "deep_understanding", "common_mistakes": ["cultural_assumptions", "inappropriate_register"], "key_concepts": ["hierarchy_respect", "indirect_communication", "relationship_building", "bureaucratic_language"]}',
    ARRAY['bureaucracy', 'healthcare', 'banking', 'housing', 'workplace', 'education', 'community', 'relationships', 'local_customs', 'legal_terms'],
    'intermediate-advanced',
    'home',
    'green',
    ARRAY['Navigate bureaucratic processes', 'Establish banking and healthcare', 'Find and secure housing', 'Integrate into workplace culture', 'Build local relationships', 'Understand legal requirements', 'Participate in community life'],
    ARRAY['Government office visits', 'Bank account opening', 'Apartment hunting', 'Workplace integration', 'School enrollment', 'Healthcare appointments', 'Community events'],
    2
),
(
    'business_professional',
    'Business Professional',
    'Focused on professional and business contexts. Develop skills for meetings, presentations, networking, and formal business communication in international settings.',
    'Business professionals, entrepreneurs, corporate employees, consultants',
    '{"focus": "professional_communication", "formality": "formal_to_business_casual", "cultural_awareness": "business_etiquette", "common_mistakes": ["inappropriate_directness", "cultural_insensitivity"], "key_concepts": ["hierarchy_respect", "meeting_protocols", "email_etiquette", "presentation_skills"]}',
    ARRAY['meetings', 'presentations', 'negotiations', 'networking', 'email_communication', 'project_management', 'client_relations', 'team_collaboration', 'industry_terms', 'financial_vocabulary'],
    'intermediate-advanced',
    'briefcase',
    'purple',
    ARRAY['Lead effective meetings', 'Deliver compelling presentations', 'Network professionally', 'Negotiate successfully', 'Communicate via email professionally', 'Manage international teams', 'Handle client relationships'],
    ARRAY['Board meetings', 'Client presentations', 'Networking events', 'Job interviews', 'Contract negotiations', 'Team collaborations', 'Conference calls'],
    3
);

-- Commit the schema changes
COMMIT; 