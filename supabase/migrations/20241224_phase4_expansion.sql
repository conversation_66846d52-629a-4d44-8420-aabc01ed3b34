-- =====================================================
-- NIRA Simulation System Phase 4 Expansion
-- Additional Personas and Advanced Features
-- Date: December 24, 2024
-- =====================================================

-- Add 5 additional personas to complete the 8 total
INSERT INTO simulation_personas (
    name, display_name, description, target_audience, cultural_context, 
    vocabulary_focus, difficulty_range, icon_name, color_theme, 
    learning_objectives, typical_scenarios, sort_order
) VALUES 
(
    'academic_student',
    'Academic & Student',
    'Designed for students and academics pursuing education abroad. Master academic vocabulary, research terminology, and university life communication.',
    'University students, researchers, PhD candidates, exchange students, academic professionals',
    '{"focus": "academic_communication", "formality": "formal_academic", "cultural_awareness": "academic_etiquette", "common_mistakes": ["informal_register", "plagiarism_concerns"], "key_concepts": ["citation_styles", "academic_integrity", "peer_review", "thesis_defense"]}',
    ARRAY['research', 'thesis', 'lectures', 'seminars', 'library', 'laboratory', 'academic_writing', 'citations', 'peer_review', 'conferences'],
    'intermediate-advanced',
    'graduationcap',
    'indigo',
    ARRAY['Navigate university systems', 'Participate in academic discussions', 'Write research papers', 'Present at conferences', 'Collaborate with peers', 'Understand academic culture', 'Use library resources'],
    ARRAY['University enrollment', 'Research presentations', 'Thesis defense', 'Academic conferences', 'Library research', 'Lab work', 'Study groups'],
    4
),
(
    'healthcare_professional',
    'Healthcare Professional',
    'Focused on medical and healthcare contexts. Develop skills for patient communication, medical terminology, and healthcare system navigation.',
    'Doctors, nurses, medical students, healthcare workers, medical researchers',
    '{"focus": "medical_communication", "formality": "professional_empathetic", "cultural_awareness": "patient_care", "common_mistakes": ["medical_jargon_overuse", "cultural_insensitivity"], "key_concepts": ["patient_confidentiality", "informed_consent", "bedside_manner", "medical_ethics"]}',
    ARRAY['symptoms', 'diagnosis', 'treatment', 'medication', 'surgery', 'emergency', 'patient_care', 'medical_records', 'insurance', 'anatomy'],
    'advanced',
    'stethoscope',
    'red',
    ARRAY['Communicate with patients', 'Explain medical procedures', 'Take medical histories', 'Provide treatment instructions', 'Handle emergencies', 'Work with medical teams', 'Navigate healthcare systems'],
    ARRAY['Patient consultations', 'Emergency situations', 'Medical team meetings', 'Surgery preparations', 'Discharge planning', 'Medical conferences', 'Insurance procedures'],
    5
),
(
    'family_parent',
    'Family & Parent',
    'Perfect for parents and families living abroad. Learn to navigate schools, healthcare, and family services while raising children in a new culture.',
    'Parents, families with children, caregivers, family immigrants',
    '{"focus": "family_communication", "formality": "casual_caring", "cultural_awareness": "family_values", "common_mistakes": ["parenting_style_conflicts", "school_system_confusion"], "key_concepts": ["child_development", "school_involvement", "family_traditions", "community_integration"]}',
    ARRAY['parenting', 'school', 'pediatrician', 'playground', 'family_activities', 'childcare', 'education', 'extracurricular', 'discipline', 'nutrition'],
    'beginner-intermediate',
    'figure.and.child.holdinghands',
    'pink',
    ARRAY['Communicate with teachers', 'Navigate school systems', 'Find pediatric care', 'Organize family activities', 'Connect with other parents', 'Handle child emergencies', 'Understand local parenting culture'],
    ARRAY['Parent-teacher conferences', 'School enrollment', 'Pediatric appointments', 'Playground interactions', 'Family outings', 'Childcare arrangements', 'Emergency situations'],
    6
),
(
    'senior_learner',
    'Senior Learner',
    'Tailored for older adults learning languages for travel, family connections, or personal enrichment. Emphasizes patience, cultural appreciation, and practical communication.',
    'Senior citizens, retirees, grandparents, older adult learners',
    '{"focus": "respectful_communication", "formality": "polite_traditional", "cultural_awareness": "intergenerational_respect", "common_mistakes": ["technology_barriers", "formal_register_confusion"], "key_concepts": ["respect_for_elders", "traditional_values", "patience_in_learning", "family_connections"]}',
    ARRAY['family', 'grandchildren', 'retirement', 'hobbies', 'health', 'traditions', 'community', 'volunteering', 'travel', 'memories'],
    'beginner',
    'person.crop.circle',
    'teal',
    ARRAY['Connect with family abroad', 'Share life experiences', 'Navigate senior services', 'Participate in community activities', 'Express personal history', 'Understand cultural traditions', 'Communicate health needs'],
    ARRAY['Family video calls', 'Community center activities', 'Medical appointments', 'Travel planning', 'Cultural events', 'Volunteer work', 'Sharing stories'],
    7
),
(
    'creative_professional',
    'Creative Professional',
    'Designed for artists, designers, writers, and creative professionals. Focus on artistic vocabulary, creative collaboration, and cultural expression.',
    'Artists, designers, writers, musicians, filmmakers, creative freelancers',
    '{"focus": "creative_expression", "formality": "casual_artistic", "cultural_awareness": "artistic_culture", "common_mistakes": ["artistic_jargon", "cultural_appropriation"], "key_concepts": ["artistic_integrity", "creative_collaboration", "cultural_sensitivity", "artistic_critique"]}',
    ARRAY['art', 'design', 'creativity', 'inspiration', 'exhibition', 'portfolio', 'collaboration', 'critique', 'aesthetics', 'expression'],
    'intermediate',
    'paintbrush',
    'orange',
    ARRAY['Discuss artistic concepts', 'Collaborate on creative projects', 'Present artistic work', 'Understand art criticism', 'Navigate creative industries', 'Express artistic vision', 'Participate in cultural events'],
    ARRAY['Art gallery openings', 'Creative collaborations', 'Portfolio presentations', 'Art critiques', 'Cultural festivals', 'Studio visits', 'Creative workshops'],
    8
);

-- Create advanced dialogue branching table
CREATE TABLE simulation_dialogue_branches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dialogue_id UUID NOT NULL REFERENCES simulation_dialogues(id) ON DELETE CASCADE,
    branch_condition JSONB NOT NULL DEFAULT '{}', -- conditions for this branch
    branch_weight DECIMAL(3,2) NOT NULL DEFAULT 1.0, -- AI weighting for branch selection
    next_dialogue_id UUID REFERENCES simulation_dialogues(id),
    branch_type VARCHAR(50) NOT NULL DEFAULT 'choice', -- 'choice', 'performance', 'ai_adaptive'
    cultural_impact DECIMAL(5,2) DEFAULT 0.0,
    difficulty_modifier DECIMAL(3,2) DEFAULT 1.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create AI variations table
CREATE TABLE simulation_ai_variations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    simulation_id UUID NOT NULL REFERENCES simulations(id) ON DELETE CASCADE,
    variation_type VARCHAR(50) NOT NULL, -- 'dialogue', 'scenario', 'assessment'
    original_content_id UUID NOT NULL, -- references dialogue, simulation, or assessment
    ai_generated_content JSONB NOT NULL DEFAULT '{}',
    variation_prompt TEXT NOT NULL,
    quality_score DECIMAL(5,2) DEFAULT 0.0,
    usage_count INTEGER DEFAULT 0,
    user_feedback JSONB DEFAULT '{}',
    is_approved BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create social features tables
CREATE TABLE simulation_shares (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    simulation_id UUID NOT NULL REFERENCES simulations(id) ON DELETE CASCADE,
    shared_by_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    shared_with_user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    share_type VARCHAR(50) NOT NULL DEFAULT 'direct', -- 'direct', 'public', 'group'
    share_message TEXT,
    is_public BOOLEAN DEFAULT false,
    view_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE simulation_groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    created_by_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    is_public BOOLEAN DEFAULT false,
    member_count INTEGER DEFAULT 1,
    simulation_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE simulation_group_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID NOT NULL REFERENCES simulation_groups(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL DEFAULT 'member', -- 'admin', 'moderator', 'member'
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(group_id, user_id)
);

CREATE TABLE simulation_group_simulations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID NOT NULL REFERENCES simulation_groups(id) ON DELETE CASCADE,
    simulation_id UUID NOT NULL REFERENCES simulations(id) ON DELETE CASCADE,
    added_by_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(group_id, simulation_id)
);

-- Enhanced analytics tables
CREATE TABLE simulation_detailed_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    simulation_id UUID NOT NULL REFERENCES simulations(id) ON DELETE CASCADE,
    session_id UUID NOT NULL DEFAULT gen_random_uuid(),
    event_type VARCHAR(100) NOT NULL, -- 'dialogue_view', 'response_selected', 'hint_used', 'replay', etc.
    event_data JSONB NOT NULL DEFAULT '{}',
    timestamp_ms BIGINT NOT NULL, -- milliseconds since session start
    user_agent TEXT,
    device_info JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE simulation_learning_insights (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    persona_id UUID NOT NULL REFERENCES simulation_personas(id) ON DELETE CASCADE,
    language_id UUID NOT NULL REFERENCES languages(id) ON DELETE CASCADE,
    insight_type VARCHAR(100) NOT NULL, -- 'strength', 'weakness', 'pattern', 'recommendation'
    insight_data JSONB NOT NULL DEFAULT '{}',
    confidence_score DECIMAL(5,2) NOT NULL DEFAULT 0.0,
    ai_generated BOOLEAN DEFAULT true,
    is_actionable BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Voice interaction enhancements
CREATE TABLE simulation_voice_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    simulation_id UUID NOT NULL REFERENCES simulations(id) ON DELETE CASCADE,
    dialogue_id UUID NOT NULL REFERENCES simulation_dialogues(id) ON DELETE CASCADE,
    audio_url VARCHAR(500),
    transcribed_text TEXT,
    pronunciation_score DECIMAL(5,2),
    fluency_score DECIMAL(5,2),
    accuracy_score DECIMAL(5,2),
    ai_feedback TEXT,
    processing_status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_dialogue_branches_dialogue ON simulation_dialogue_branches(dialogue_id);
CREATE INDEX idx_ai_variations_simulation ON simulation_ai_variations(simulation_id);
CREATE INDEX idx_ai_variations_type ON simulation_ai_variations(variation_type);
CREATE INDEX idx_shares_simulation ON simulation_shares(simulation_id);
CREATE INDEX idx_shares_user ON simulation_shares(shared_by_user_id);
CREATE INDEX idx_group_members_group ON simulation_group_members(group_id);
CREATE INDEX idx_group_members_user ON simulation_group_members(user_id);
CREATE INDEX idx_detailed_analytics_user_simulation ON simulation_detailed_analytics(user_id, simulation_id);
CREATE INDEX idx_detailed_analytics_session ON simulation_detailed_analytics(session_id);
CREATE INDEX idx_learning_insights_user ON simulation_learning_insights(user_id);
CREATE INDEX idx_voice_interactions_user_simulation ON simulation_voice_interactions(user_id, simulation_id);

-- Add RLS policies
ALTER TABLE simulation_dialogue_branches ENABLE ROW LEVEL SECURITY;
ALTER TABLE simulation_ai_variations ENABLE ROW LEVEL SECURITY;
ALTER TABLE simulation_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE simulation_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE simulation_group_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE simulation_group_simulations ENABLE ROW LEVEL SECURITY;
ALTER TABLE simulation_detailed_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE simulation_learning_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE simulation_voice_interactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Public read access to dialogue branches" ON simulation_dialogue_branches
    FOR SELECT USING (true);

CREATE POLICY "Public read access to approved AI variations" ON simulation_ai_variations
    FOR SELECT USING (is_approved = true);

CREATE POLICY "Users can manage their own shares" ON simulation_shares
    FOR ALL USING (auth.uid() = shared_by_user_id OR auth.uid() = shared_with_user_id);

CREATE POLICY "Users can view public groups" ON simulation_groups
    FOR SELECT USING (is_public = true OR created_by_user_id = auth.uid());

CREATE POLICY "Users can manage their group memberships" ON simulation_group_members
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their analytics data" ON simulation_detailed_analytics
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their learning insights" ON simulation_learning_insights
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their voice interactions" ON simulation_voice_interactions
    FOR ALL USING (auth.uid() = user_id);

-- Update triggers for timestamp management
CREATE TRIGGER update_ai_variations_updated_at BEFORE UPDATE ON simulation_ai_variations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_simulation_groups_updated_at BEFORE UPDATE ON simulation_groups
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

COMMIT; 