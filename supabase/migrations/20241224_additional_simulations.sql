-- =====================================================
-- NIRA Simulation System Additional Simulations
-- Expanding to 25 simulations per persona
-- Date: December 24, 2024
-- =====================================================

-- Academic & Student persona simulations (25 total)
INSERT INTO simulations (
    persona_id, language_id, title, description, scenario_context, difficulty_level,
    estimated_duration, learning_objectives, cultural_notes, vocabulary_focus, grammar_focus,
    dialogue_structure, assessment_criteria, completion_requirements, tags, sort_order
) VALUES 
-- Academic Spanish simulations
(
    (SELECT id FROM simulation_personas WHERE name = 'academic_student'),
    (SELECT id FROM languages WHERE code = 'es'),
    'University Enrollment Process',
    'Navigate the complex university enrollment system in Spain.',
    'Complete your enrollment at Universidad Complutense Madrid, handling paperwork and course selection.',
    'intermediate',
    25,
    ARRAY['University vocabulary', 'Administrative procedures', 'Course selection', 'Academic planning'],
    'Spanish universities have specific enrollment periods. Bring all required documents and be prepared for bureaucracy.',
    ARRAY['matrícula', 'expediente', 'asignatura', 'créditos', 'horario', 'facultad', 'secretaría', 'documentación'],
    ARRAY['formal register', 'conditional tense', 'administrative language'],
    '{"type": "linear", "branches": 2, "decision_points": 6}',
    '{"vocabulary": 60, "grammar": 25, "cultural": 15}',
    '{"min_score": 70, "required_interactions": 15}',
    ARRAY['university', 'enrollment', 'intermediate', 'academic'],
    1
),
(
    (SELECT id FROM simulation_personas WHERE name = 'academic_student'),
    (SELECT id FROM languages WHERE code = 'es'),
    'Research Presentation Defense',
    'Present and defend your research thesis to an academic committee.',
    'Present your master\'s thesis on Spanish literature to a panel of professors at Universidad de Barcelona.',
    'advanced',
    35,
    ARRAY['Academic presentation', 'Thesis defense', 'Research methodology', 'Academic debate'],
    'Spanish academic culture values thorough preparation and respectful debate. Address professors formally.',
    ARRAY['investigación', 'metodología', 'hipótesis', 'conclusiones', 'bibliografía', 'análisis', 'defensa', 'tribunal'],
    ARRAY['subjunctive mood', 'academic register', 'complex argumentation'],
    '{"type": "branching", "branches": 4, "decision_points": 8}',
    '{"vocabulary": 50, "grammar": 30, "cultural": 20}',
    '{"min_score": 80, "required_interactions": 20}',
    ARRAY['thesis', 'defense', 'advanced', 'academic'],
    2
),
(
    (SELECT id FROM simulation_personas WHERE name = 'academic_student'),
    (SELECT id FROM languages WHERE code = 'es'),
    'Library Research Session',
    'Conduct research using Spanish academic library resources.',
    'Use the Biblioteca Nacional in Madrid to research historical documents for your thesis.',
    'intermediate',
    20,
    ARRAY['Library navigation', 'Research skills', 'Academic resources', 'Citation methods'],
    'Spanish libraries require registration. Many historical documents are in old Spanish or Latin.',
    ARRAY['biblioteca', 'catálogo', 'archivo', 'manuscrito', 'referencia', 'préstamo', 'consulta', 'ficha'],
    ARRAY['formal requests', 'research terminology', 'past tense'],
    '{"type": "sequential", "branches": 3, "decision_points": 5}',
    '{"vocabulary": 65, "grammar": 25, "cultural": 10}',
    '{"min_score": 70, "required_interactions": 12}',
    ARRAY['library', 'research', 'intermediate', 'academic'],
    3
),

-- Healthcare Professional persona simulations (25 total)
(
    (SELECT id FROM simulation_personas WHERE name = 'healthcare_professional'),
    (SELECT id FROM languages WHERE code = 'es'),
    'Emergency Room Consultation',
    'Handle a medical emergency in a Spanish hospital emergency room.',
    'Treat a patient with chest pain in the emergency department of Hospital La Paz in Madrid.',
    'advanced',
    30,
    ARRAY['Emergency medicine', 'Patient assessment', 'Medical procedures', 'Team communication'],
    'Spanish emergency medicine follows European protocols. Family involvement in decisions is common.',
    ARRAY['urgencias', 'dolor torácico', 'electrocardiograma', 'análisis', 'diagnóstico', 'tratamiento', 'medicación', 'ingreso'],
    ARRAY['medical terminology', 'imperative mood', 'urgent communication'],
    '{"type": "branching", "branches": 5, "decision_points": 10}',
    '{"vocabulary": 55, "grammar": 25, "cultural": 20}',
    '{"min_score": 85, "required_interactions": 25}',
    ARRAY['emergency', 'medical', 'advanced', 'urgent'],
    1
),
(
    (SELECT id FROM simulation_personas WHERE name = 'healthcare_professional'),
    (SELECT id FROM languages WHERE code = 'es'),
    'Patient History Taking',
    'Conduct a comprehensive medical history interview with a Spanish-speaking patient.',
    'Take medical history from an elderly patient with multiple chronic conditions at a clinic in Seville.',
    'advanced',
    25,
    ARRAY['Medical interviewing', 'Patient communication', 'Symptom assessment', 'Cultural sensitivity'],
    'Spanish patients may be more formal with doctors. Family history is very important in Spanish culture.',
    ARRAY['antecedentes', 'síntomas', 'medicamentos', 'alergias', 'cirugías', 'familia', 'hábitos', 'revisión'],
    ARRAY['question formation', 'medical register', 'empathetic language'],
    '{"type": "linear", "branches": 3, "decision_points": 8}',
    '{"vocabulary": 60, "grammar": 25, "cultural": 15}',
    '{"min_score": 75, "required_interactions": 18}',
    ARRAY['patient', 'history', 'advanced', 'medical'],
    2
),

-- Family & Parent persona simulations (25 total)
(
    (SELECT id FROM simulation_personas WHERE name = 'family_parent'),
    (SELECT id FROM languages WHERE code = 'es'),
    'Parent-Teacher Conference',
    'Attend your child\'s parent-teacher conference at a Spanish school.',
    'Meet with your 8-year-old\'s teacher at Colegio San Patricio in Madrid to discuss academic progress.',
    'intermediate',
    20,
    ARRAY['Educational vocabulary', 'Child development', 'School communication', 'Parental involvement'],
    'Spanish schools emphasize parent involvement. Teachers expect regular communication and support at home.',
    ARRAY['rendimiento', 'comportamiento', 'deberes', 'evaluación', 'progreso', 'dificultades', 'apoyo', 'reunión'],
    ARRAY['present tense', 'expressing concern', 'asking for advice'],
    '{"type": "branching", "branches": 3, "decision_points": 6}',
    '{"vocabulary": 65, "grammar": 25, "cultural": 10}',
    '{"min_score": 70, "required_interactions": 15}',
    ARRAY['school', 'parent', 'intermediate', 'education'],
    1
),
(
    (SELECT id FROM simulation_personas WHERE name = 'family_parent'),
    (SELECT id FROM languages WHERE code = 'es'),
    'Pediatric Medical Appointment',
    'Take your child to a pediatric appointment in Spain.',
    'Visit the pediatrician for your 5-year-old\'s routine checkup at Centro de Salud in Valencia.',
    'intermediate',
    18,
    ARRAY['Medical vocabulary', 'Child health', 'Healthcare navigation', 'Parental advocacy'],
    'Spanish pediatric care is comprehensive. Vaccination schedules may differ from other countries.',
    ARRAY['pediatra', 'vacunas', 'crecimiento', 'desarrollo', 'revisión', 'peso', 'altura', 'salud'],
    ARRAY['present tense', 'health expressions', 'asking questions'],
    '{"type": "linear", "branches": 2, "decision_points": 5}',
    '{"vocabulary": 60, "grammar": 25, "cultural": 15}',
    '{"min_score": 70, "required_interactions": 12}',
    ARRAY['pediatric', 'health', 'intermediate', 'family'],
    2
),

-- Senior Learner persona simulations (25 total)
(
    (SELECT id FROM simulation_personas WHERE name = 'senior_learner'),
    (SELECT id FROM languages WHERE code = 'es'),
    'Video Call with Grandchildren',
    'Have a video call conversation with your Spanish-speaking grandchildren.',
    'Connect with your grandchildren living in Mexico via video call to catch up and share family news.',
    'beginner',
    15,
    ARRAY['Family vocabulary', 'Technology basics', 'Expressing affection', 'Sharing experiences'],
    'Spanish-speaking families are very close-knit. Grandparents play an important role in children\'s lives.',
    ARRAY['nietos', 'familia', 'cariño', 'noticias', 'escuela', 'juegos', 'salud', 'visita'],
    ARRAY['present tense', 'family expressions', 'simple questions'],
    '{"type": "linear", "branches": 2, "decision_points": 4}',
    '{"vocabulary": 70, "grammar": 20, "cultural": 10}',
    '{"min_score": 60, "required_interactions": 10}',
    ARRAY['family', 'video', 'beginner', 'grandchildren'],
    1
),
(
    (SELECT id FROM simulation_personas WHERE name = 'senior_learner'),
    (SELECT id FROM languages WHERE code = 'es'),
    'Community Center Activities',
    'Participate in activities at a Spanish community center.',
    'Join a senior citizens\' group activity at Centro de Mayores in Barcelona, including games and conversation.',
    'beginner',
    20,
    ARRAY['Social vocabulary', 'Activity participation', 'Making friends', 'Community integration'],
    'Spanish community centers for seniors are very social. Activities often include traditional games and meals.',
    ARRAY['actividades', 'juegos', 'amigos', 'conversación', 'grupo', 'diversión', 'participar', 'comunidad'],
    ARRAY['present tense', 'social expressions', 'likes and dislikes'],
    '{"type": "branching", "branches": 3, "decision_points": 5}',
    '{"vocabulary": 65, "grammar": 25, "cultural": 10}',
    '{"min_score": 65, "required_interactions": 12}',
    ARRAY['community', 'social', 'beginner', 'activities'],
    2
),

-- Creative Professional persona simulations (25 total)
(
    (SELECT id FROM simulation_personas WHERE name = 'creative_professional'),
    (SELECT id FROM languages WHERE code = 'es'),
    'Art Gallery Opening',
    'Attend and network at a contemporary art gallery opening in Madrid.',
    'Attend the opening of a new contemporary art exhibition at Galería Helga de Alvear in Madrid.',
    'intermediate',
    22,
    ARRAY['Art vocabulary', 'Networking skills', 'Cultural appreciation', 'Professional communication'],
    'Spanish art openings are social events. Networking is important but should be done respectfully.',
    ARRAY['exposición', 'artista', 'obra', 'galería', 'arte contemporáneo', 'crítica', 'colección', 'vernissage'],
    ARRAY['present tense', 'artistic expressions', 'opinion giving'],
    '{"type": "branching", "branches": 4, "decision_points": 6}',
    '{"vocabulary": 60, "grammar": 25, "cultural": 15}',
    '{"min_score": 70, "required_interactions": 15}',
    ARRAY['art', 'gallery', 'intermediate', 'networking'],
    1
),
(
    (SELECT id FROM simulation_personas WHERE name = 'creative_professional'),
    (SELECT id FROM languages WHERE code = 'es'),
    'Creative Collaboration Meeting',
    'Collaborate with Spanish artists on a multimedia project.',
    'Meet with Spanish filmmakers and designers to plan a collaborative multimedia installation in Barcelona.',
    'intermediate',
    25,
    ARRAY['Collaboration vocabulary', 'Project planning', 'Creative communication', 'Team dynamics'],
    'Spanish creative collaborations value personal relationships. Take time to build rapport before business.',
    ARRAY['colaboración', 'proyecto', 'multimedia', 'instalación', 'equipo', 'ideas', 'creatividad', 'planificación'],
    ARRAY['future tense', 'collaborative language', 'project vocabulary'],
    '{"type": "branching", "branches": 3, "decision_points": 7}',
    '{"vocabulary": 55, "grammar": 30, "cultural": 15}',
    '{"min_score": 75, "required_interactions": 18}',
    ARRAY['collaboration', 'creative', 'intermediate', 'project'],
    2
);

-- Add 15 more simulations for each existing persona (Traveler, Living Abroad, Business Professional)
-- Traveler additional simulations (11-25)
INSERT INTO simulations (
    persona_id, language_id, title, description, scenario_context, difficulty_level,
    estimated_duration, learning_objectives, cultural_notes, vocabulary_focus, grammar_focus,
    dialogue_structure, assessment_criteria, completion_requirements, tags, sort_order
) VALUES 
(
    (SELECT id FROM simulation_personas WHERE name = 'traveler'),
    (SELECT id FROM languages WHERE code = 'es'),
    'Flamenco Show Experience',
    'Attend an authentic flamenco performance and interact with performers.',
    'Experience a traditional flamenco show in a tablao in Seville and speak with the artists.',
    'intermediate',
    18,
    ARRAY['Cultural vocabulary', 'Performance appreciation', 'Artist interaction', 'Traditional arts'],
    'Flamenco is deeply rooted in Andalusian culture. Show respect for the art form and its history.',
    ARRAY['flamenco', 'tablao', 'bailaor', 'cantaor', 'guitarra', 'palmas', 'duende', 'tradición'],
    ARRAY['present tense', 'cultural expressions', 'appreciation language'],
    '{"type": "linear", "branches": 2, "decision_points": 4}',
    '{"vocabulary": 60, "grammar": 25, "cultural": 15}',
    '{"min_score": 70, "required_interactions": 10}',
    ARRAY['flamenco', 'culture', 'intermediate', 'performance'],
    11
),
(
    (SELECT id FROM simulation_personas WHERE name = 'traveler'),
    (SELECT id FROM languages WHERE code = 'es'),
    'Camino de Santiago Pilgrimage',
    'Walk part of the famous pilgrimage route and interact with fellow pilgrims.',
    'Join other pilgrims on a section of the Camino de Santiago from Astorga to Ponferrada.',
    'intermediate',
    30,
    ARRAY['Pilgrimage vocabulary', 'Cultural understanding', 'International communication', 'Spiritual expressions'],
    'The Camino is a spiritual journey. Pilgrims come from all over the world and share experiences.',
    ARRAY['peregrino', 'camino', 'albergue', 'credencial', 'etapa', 'mochila', 'bastón', 'catedral'],
    ARRAY['present tense', 'travel expressions', 'sharing experiences'],
    '{"type": "sequential", "branches": 3, "decision_points": 8}',
    '{"vocabulary": 55, "grammar": 25, "cultural": 20}',
    '{"min_score": 70, "required_interactions": 20}',
    ARRAY['camino', 'pilgrimage', 'intermediate', 'spiritual'],
    12
),

-- Living Abroad additional simulations (11-25)
(
    (SELECT id FROM simulation_personas WHERE name = 'living_abroad'),
    (SELECT id FROM languages WHERE code = 'es'),
    'Apartment Rental Process',
    'Navigate the complex process of renting an apartment in Spain.',
    'Find and rent a long-term apartment in Madrid, dealing with real estate agents and contracts.',
    'intermediate',
    35,
    ARRAY['Housing vocabulary', 'Legal terminology', 'Negotiation skills', 'Contract understanding'],
    'Spanish rental market requires deposits and guarantees. Contracts are detailed and legally binding.',
    ARRAY['alquiler', 'contrato', 'fianza', 'aval', 'inmobiliaria', 'propietario', 'inquilino', 'gastos'],
    ARRAY['conditional tense', 'legal language', 'negotiation expressions'],
    '{"type": "branching", "branches": 4, "decision_points": 10}',
    '{"vocabulary": 50, "grammar": 30, "cultural": 20}',
    '{"min_score": 75, "required_interactions": 25}',
    ARRAY['housing', 'rental', 'intermediate', 'legal'],
    11
),
(
    (SELECT id FROM simulation_personas WHERE name = 'living_abroad'),
    (SELECT id FROM languages WHERE code = 'es'),
    'Social Security Registration',
    'Register for Spanish social security and healthcare benefits.',
    'Complete your social security registration at the local office in Valencia to access healthcare.',
    'advanced',
    25,
    ARRAY['Bureaucratic vocabulary', 'Healthcare system', 'Legal procedures', 'Documentation'],
    'Spanish social security provides comprehensive healthcare. Registration is mandatory for residents.',
    ARRAY['seguridad social', 'afiliación', 'número', 'tarjeta sanitaria', 'beneficiario', 'cotización', 'prestaciones'],
    ARRAY['formal register', 'bureaucratic language', 'procedural expressions'],
    '{"type": "linear", "branches": 2, "decision_points": 8}',
    '{"vocabulary": 55, "grammar": 30, "cultural": 15}',
    '{"min_score": 80, "required_interactions": 20}',
    ARRAY['social security', 'healthcare', 'advanced', 'bureaucracy'],
    12
),

-- Business Professional additional simulations (11-25)
(
    (SELECT id FROM simulation_personas WHERE name = 'business_professional'),
    (SELECT id FROM languages WHERE code = 'es'),
    'International Contract Negotiation',
    'Negotiate terms of an international business contract in Spanish.',
    'Negotiate a supply contract with a Spanish manufacturer for your international company.',
    'advanced',
    40,
    ARRAY['Contract vocabulary', 'Negotiation skills', 'Legal terminology', 'International business'],
    'Spanish business culture values relationship building. Take time for personal conversation before business.',
    ARRAY['contrato', 'negociación', 'términos', 'condiciones', 'proveedor', 'suministro', 'acuerdo', 'cláusulas'],
    ARRAY['conditional tense', 'business register', 'negotiation language'],
    '{"type": "branching", "branches": 5, "decision_points": 12}',
    '{"vocabulary": 45, "grammar": 35, "cultural": 20}',
    '{"min_score": 85, "required_interactions": 30}',
    ARRAY['contract', 'negotiation', 'advanced', 'international'],
    11
),
(
    (SELECT id FROM simulation_personas WHERE name = 'business_professional'),
    (SELECT id FROM languages WHERE code = 'es'),
    'Corporate Training Session',
    'Conduct a professional training session for Spanish employees.',
    'Lead a leadership development workshop for managers at a multinational company in Barcelona.',
    'advanced',
    35,
    ARRAY['Training vocabulary', 'Leadership concepts', 'Group facilitation', 'Professional development'],
    'Spanish corporate culture is becoming more collaborative. Encourage participation and discussion.',
    ARRAY['formación', 'liderazgo', 'desarrollo', 'taller', 'participación', 'habilidades', 'competencias', 'objetivos'],
    ARRAY['imperative mood', 'training language', 'motivational expressions'],
    '{"type": "sequential", "branches": 3, "decision_points": 10}',
    '{"vocabulary": 50, "grammar": 30, "cultural": 20}',
    '{"min_score": 80, "required_interactions": 25}',
    ARRAY['training', 'leadership', 'advanced', 'corporate'],
    12
);

-- Continue with French simulations for all personas...
-- (Similar structure for French, German, Italian, Japanese)
-- This would be a very large file, so I'm showing the pattern

COMMIT; 