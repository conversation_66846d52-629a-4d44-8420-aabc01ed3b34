-- ===============================================
-- NIRA Language Learning Platform
-- New Languages Content Population
-- Sample vocabulary, cultural contexts, and lessons for new languages
-- ===============================================

-- First, ensure we have the vocabulary and cultural_contexts tables structure
-- (These should already exist from the main schema)

-- ===============================================
-- KOREAN CONTENT (<PERSON><PERSON>'s expertise)
-- ===============================================

-- Korean Vocabulary
INSERT INTO vocabulary (language_id, word, pronunciation, translation, part_of_speech, difficulty_level, frequency_rank, example_sentences, cultural_context, tags)
SELECT 
    l.id,
    vocab.word,
    vocab.pronunciation,
    vocab.translation::jsonb,
    vocab.part_of_speech,
    vocab.difficulty_level,
    vocab.frequency_rank,
    vocab.example_sentences::jsonb,
    vocab.cultural_context,
    vocab.tags
FROM languages l
CROSS JOIN (
    VALUES 
    ('안녕하세요', 'an-nyeong-ha-se-yo', '{"en": "hello (formal)", "es": "hola (formal)"}', 'interjection', 1, 1, '["안녕하세요! 만나서 반갑습니다.", "안녕하세요, 어떻게 지내세요?"]', 'Formal greeting showing respect, essential in Korean culture', '{"greetings", "formal", "respect"}'),
    ('감사합니다', 'gam-sa-ham-ni-da', '{"en": "thank you (formal)", "es": "gracias (formal)"}', 'interjection', 1, 2, '["도움 주셔서 감사합니다.", "정말 감사합니다!"]', 'Formal gratitude expression, shows proper manners', '{"politeness", "formal", "gratitude"}'),
    ('카페', 'ka-pe', '{"en": "cafe", "es": "café"}', 'noun', 1, 50, '["카페에서 커피 마실래요?", "이 카페 분위기가 좋아요."]', 'Korean cafe culture is huge, especially among young people', '{"food", "culture", "social"}'),
    ('한국어', 'han-guk-eo', '{"en": "Korean language", "es": "idioma coreano"}', 'noun', 2, 100, '["한국어를 배우고 있어요.", "한국어가 어려워요."]', 'Learning Korean shows respect for Korean culture', '{"language", "learning", "culture"}'),
    ('김치', 'gim-chi', '{"en": "kimchi", "es": "kimchi"}', 'noun', 1, 80, '["김치가 매워요.", "김치찌개 먹고 싶어요."]', 'National dish of Korea, symbol of Korean identity', '{"food", "culture", "traditional"}')
) AS vocab(word, pronunciation, translation, part_of_speech, difficulty_level, frequency_rank, example_sentences, cultural_context, tags)
WHERE l.code = 'ko';

-- Korean Cultural Context
INSERT INTO cultural_contexts (language_id, topic, category, explanation, examples, do_dont_tips, regional_variations, agent_notes, difficulty_level, tags)
SELECT 
    l.id,
    'Korean Honorifics System',
    'social',
    'Korean language has complex honorific system based on age, social status, and relationship. Using proper honorifics shows respect and cultural understanding.',
    '{"formal_speech": ["안녕하세요 (annyeonghaseyo)", "감사합니다 (gamsahamnida)"], "informal_speech": ["안녕 (annyeong)", "고마워 (gomawo)"], "honorific_titles": ["선생님 (seonsaengnim)", "형/누나 (hyeong/nuna)"]}',
    '{"do": ["Use formal speech with elders", "Add 님 (nim) to titles", "Bow when greeting", "Use both hands when giving/receiving"], "dont": ["Use informal speech with strangers", "Ignore age hierarchy", "Point with one finger", "Refuse food/drink offers directly"]}',
    'Seoul dialect is standard, but regional dialects exist in Busan, Jeju, etc.',
    '{"minji_notes": "Honorifics are the heart of Korean communication! I always teach students that showing respect through language opens hearts and builds relationships.", "cultural_insight": "Korean society values harmony and respect, reflected deeply in language structure"}',
    2,
    '{"honorifics", "respect", "social", "hierarchy"}'
FROM languages l WHERE l.code = 'ko';

-- ===============================================
-- ITALIAN CONTENT (Marco's expertise)
-- ===============================================

-- Italian Vocabulary
INSERT INTO vocabulary (language_id, word, pronunciation, translation, part_of_speech, difficulty_level, frequency_rank, example_sentences, cultural_context, tags)
SELECT 
    l.id,
    vocab.word,
    vocab.pronunciation,
    vocab.translation::jsonb,
    vocab.part_of_speech,
    vocab.difficulty_level,
    vocab.frequency_rank,
    vocab.example_sentences::jsonb,
    vocab.cultural_context,
    vocab.tags
FROM languages l
CROSS JOIN (
    VALUES 
    ('ciao', 'chah-oh', '{"en": "hello/goodbye", "es": "hola/adiós"}', 'interjection', 1, 1, '["Ciao! Come stai?", "Ciao, ci vediamo dopo!"]', 'Informal greeting used among friends and family', '{"greetings", "informal", "friendly"}'),
    ('grazie', 'grah-tsee-eh', '{"en": "thank you", "es": "gracias"}', 'interjection', 1, 2, '["Grazie mille!", "Grazie per l\'aiuto."]', 'Essential politeness in Italian culture', '{"politeness", "gratitude", "basic"}'),
    ('pasta', 'pah-stah', '{"en": "pasta", "es": "pasta"}', 'noun', 1, 30, '["La pasta è buonissima!", "Mangiamo la pasta stasera."]', 'Central to Italian cuisine and family meals', '{"food", "culture", "family"}'),
    ('famiglia', 'fah-mee-lyah', '{"en": "family", "es": "familia"}', 'noun', 1, 40, '["La famiglia è importante.", "Domenica pranziamo in famiglia."]', 'Family is cornerstone of Italian society', '{"family", "values", "culture"}'),
    ('bello', 'beh-loh', '{"en": "beautiful", "es": "hermoso"}', 'adjective', 2, 60, '["Che bello!", "È un posto molto bello."]', 'Italians appreciate beauty in all forms', '{"beauty", "appreciation", "culture"}')
) AS vocab(word, pronunciation, translation, part_of_speech, difficulty_level, frequency_rank, example_sentences, cultural_context, tags)
WHERE l.code = 'it';

-- Italian Cultural Context
INSERT INTO cultural_contexts (language_id, topic, category, explanation, examples, do_dont_tips, regional_variations, agent_notes, difficulty_level, tags)
SELECT 
    l.id,
    'Italian Meal Culture',
    'dining',
    'Italian meals are sacred family time with specific customs and timing. Understanding meal culture is essential for Italian social integration.',
    '{"meal_times": ["Colazione (8-10am)", "Pranzo (12:30-2pm)", "Cena (7:30-9pm)"], "courses": ["Antipasto", "Primo", "Secondo", "Dolce"], "customs": ["Never cappuccino after meals", "Pasta is primo, not main dish", "Family gathers for Sunday lunch"]}',
    '{"do": ["Eat pasta with fork only", "Wait for tutti a tavola", "Compliment the cook", "Take time to enjoy"], "dont": ["Rush through meals", "Ask for parmesan on seafood pasta", "Drink cappuccino after 11am", "Eat pizza with hands at dinner"]}',
    'Northern Italy: risotto culture; Southern Italy: more pasta and seafood; Sicily: unique Arab influences',
    '{"marco_notes": "Food is love in Italian culture! Every meal is a celebration of family and tradition. I teach students that understanding our food culture opens doors to Italian hearts.", "teaching_approach": "I use food examples to teach language because it connects to emotions and memories"}',
    2,
    '{"food", "family", "culture", "traditions"}'
FROM languages l WHERE l.code = 'it';

-- ===============================================
-- GERMAN CONTENT (Greta's expertise)
-- ===============================================

-- German Vocabulary
INSERT INTO vocabulary (language_id, word, pronunciation, translation, part_of_speech, difficulty_level, frequency_rank, example_sentences, cultural_context, tags)
SELECT 
    l.id,
    vocab.word,
    vocab.pronunciation,
    vocab.translation::jsonb,
    vocab.part_of_speech,
    vocab.difficulty_level,
    vocab.frequency_rank,
    vocab.example_sentences::jsonb,
    vocab.cultural_context,
    vocab.tags
FROM languages l
CROSS JOIN (
    VALUES 
    ('Guten Tag', 'goo-ten tahk', '{"en": "good day", "es": "buen día"}', 'interjection', 1, 1, '["Guten Tag! Wie geht es Ihnen?", "Guten Tag, schönes Wetter heute."]', 'Standard formal greeting in German-speaking countries', '{"greetings", "formal", "polite"}'),
    ('danke', 'dahn-keh', '{"en": "thank you", "es": "gracias"}', 'interjection', 1, 2, '["Danke schön!", "Vielen Dank für Ihre Hilfe."]', 'Politeness is highly valued in German culture', '{"politeness", "gratitude", "basic"}'),
    ('Ordnung', 'or-dnung', '{"en": "order/organization", "es": "orden"}', 'noun', 3, 200, '["Ordnung muss sein.", "Hier herrscht Ordnung."]', 'Core German value of organization and structure', '{"values", "culture", "organization"}'),
    ('Pünktlichkeit', 'puenkt-likh-kite', '{"en": "punctuality", "es": "puntualidad"}', 'noun', 3, 250, '["Pünktlichkeit ist wichtig.", "Deutsche schätzen Pünktlichkeit."]', 'Being on time is crucial in German culture', '{"values", "time", "respect"}'),
    ('Gemütlichkeit', 'geh-muet-likh-kite', '{"en": "coziness/warmth", "es": "ambiente acogedor"}', 'noun', 3, 300, '["Das ist echte Gemütlichkeit.", "Hier fühle ich Gemütlichkeit."]', 'German concept of warmth, belonging, and good cheer', '{"culture", "emotion", "atmosphere"}'
) AS vocab(word, pronunciation, translation, part_of_speech, difficulty_level, frequency_rank, example_sentences, cultural_context, tags)
WHERE l.code = 'de';

-- ===============================================
-- HINDI CONTENT (Priya's expertise)
-- ===============================================

-- Hindi Vocabulary
INSERT INTO vocabulary (language_id, word, pronunciation, translation, part_of_speech, difficulty_level, frequency_rank, example_sentences, cultural_context, tags)
SELECT 
    l.id,
    vocab.word,
    vocab.pronunciation,
    vocab.translation::jsonb,
    vocab.part_of_speech,
    vocab.difficulty_level,
    vocab.frequency_rank,
    vocab.example_sentences::jsonb,
    vocab.cultural_context,
    vocab.tags
FROM languages l
CROSS JOIN (
    VALUES 
    ('नमस्ते', 'na-mas-te', '{"en": "hello/goodbye", "es": "hola/adiós"}', 'interjection', 1, 1, '["नमस्ते! आप कैसे हैं?", "नमस्ते, फिर मिलेंगे।"]', 'Universal greeting showing respect, hands joined in prayer position', '{"greetings", "respect", "spiritual"}'),
    ('धन्यवाद', 'dhan-ya-vaad', '{"en": "thank you", "es": "gracias"}', 'interjection', 1, 2, '["बहुत धन्यवाद!", "आपका धन्यवाद।"]', 'Formal gratitude expression in Hindi', '{"politeness", "gratitude", "formal"}'),
    ('परिवार', 'pa-ri-vaar', '{"en": "family", "es": "familia"}', 'noun', 1, 20, '["परिवार सबसे महत्वपूर्ण है।", "मेरा परिवार बड़ा है।"]', 'Family is central to Indian culture and values', '{"family", "values", "culture"}'),
    ('त्योहार', 'tyo-haar', '{"en": "festival", "es": "festival"}', 'noun', 2, 100, '["दिवाली एक बड़ा त्योहार है।", "हम त्योहार मनाते हैं।"]', 'Festivals are integral to Indian cultural and spiritual life', '{"culture", "celebration", "spiritual"}'),
    ('आदर', 'aa-dar', '{"en": "respect", "es": "respeto"}', 'noun', 2, 150, '["बड़ों का आदर करना चाहिए।", "आदर से बात करें।"]', 'Respect for elders and authority is fundamental in Indian culture', '{"values", "respect", "hierarchy"}'
) AS vocab(word, pronunciation, translation, part_of_speech, difficulty_level, frequency_rank, example_sentences, cultural_context, tags)
WHERE l.code = 'hi';

-- ===============================================
-- CHINESE CONTENT (Wei's expertise)
-- ===============================================

-- Chinese Vocabulary
INSERT INTO vocabulary (language_id, word, pronunciation, translation, part_of_speech, difficulty_level, frequency_rank, example_sentences, cultural_context, tags)
SELECT 
    l.id,
    vocab.word,
    vocab.pronunciation,
    vocab.translation::jsonb,
    vocab.part_of_speech,
    vocab.difficulty_level,
    vocab.frequency_rank,
    vocab.example_sentences::jsonb,
    vocab.cultural_context,
    vocab.tags
FROM languages l
CROSS JOIN (
    VALUES 
    ('你好', 'nǐ hǎo', '{"en": "hello", "es": "hola"}', 'interjection', 1, 1, '["你好！你好吗？", "你好，很高兴见到你。"]', 'Standard greeting in Mandarin Chinese', '{"greetings", "basic", "polite"}'),
    ('谢谢', 'xiè xiè', '{"en": "thank you", "es": "gracias"}', 'interjection', 1, 2, '["谢谢你！", "非常谢谢。"]', 'Essential politeness in Chinese culture', '{"politeness", "gratitude", "basic"}'),
    ('和谐', 'hé xié', '{"en": "harmony", "es": "armonía"}', 'noun', 3, 200, '["社会需要和谐。", "家庭和谐很重要。"]', 'Core Chinese philosophical concept of balance and peace', '{"philosophy", "values", "culture"}'),
    ('尊重', 'zūn zhòng', '{"en": "respect", "es": "respeto"}', 'noun', 2, 100, '["我们要尊重长辈。", "相互尊重很重要。"]', 'Respect, especially for elders, is fundamental in Chinese culture', '{"values", "respect", "hierarchy"}'),
    ('茶', 'chá', '{"en": "tea", "es": "té"}', 'noun', 1, 50, '["我喜欢喝茶。", "中国茶很有名。"]', 'Tea culture is central to Chinese social and philosophical life', '{"culture", "tradition", "social"}'
) AS vocab(word, pronunciation, translation, part_of_speech, difficulty_level, frequency_rank, example_sentences, cultural_context, tags)
WHERE l.code = 'zh';

-- ===============================================
-- PORTUGUESE CONTENT (Sofia's expertise)
-- ===============================================

-- Portuguese Vocabulary
INSERT INTO vocabulary (language_id, word, pronunciation, translation, part_of_speech, difficulty_level, frequency_rank, example_sentences, cultural_context, tags)
SELECT 
    l.id,
    vocab.word,
    vocab.pronunciation,
    vocab.translation::jsonb,
    vocab.part_of_speech,
    vocab.difficulty_level,
    vocab.frequency_rank,
    vocab.example_sentences::jsonb,
    vocab.cultural_context,
    vocab.tags
FROM languages l
CROSS JOIN (
    VALUES 
    ('olá', 'oh-LAH', '{"en": "hello", "es": "hola"}', 'interjection', 1, 1, '["Olá! Como você está?", "Olá, tudo bem?"]', 'Friendly greeting in Brazilian Portuguese', '{"greetings", "friendly", "basic"}'),
    ('obrigado', 'oh-bree-GAH-doo', '{"en": "thank you (male)", "es": "gracias"}', 'interjection', 1, 2, '["Muito obrigado!", "Obrigado pela ajuda."]', 'Gratitude expression, changes with speaker gender', '{"politeness", "gratitude", "gender"}'),
    ('saudade', 'sah-oo-DAH-jee', '{"en": "longing/nostalgia", "es": "nostalgia"}', 'noun', 3, 300, '["Tenho saudade de casa.", "Saudade é um sentimento brasileiro."]', 'Uniquely Portuguese emotion of bittersweet longing', '{"emotion", "culture", "unique"}'),
    ('festa', 'FEH-stah', '{"en": "party", "es": "fiesta"}', 'noun', 1, 80, '["Vamos fazer uma festa!", "A festa foi incrível."]', 'Celebration is central to Brazilian culture', '{"celebration", "culture", "social"}'),
    ('família', 'fah-MEE-lee-ah', '{"en": "family", "es": "familia"}', 'noun', 1, 40, '["A família é tudo.", "Domingo é dia de família."]', 'Family bonds are extremely strong in Brazilian culture', '{"family", "values", "culture"}'
) AS vocab(word, pronunciation, translation, part_of_speech, difficulty_level, frequency_rank, example_sentences, cultural_context, tags)
WHERE l.code = 'pt';

-- ===============================================
-- TELUGU CONTENT (Ravi's expertise)
-- ===============================================

-- Telugu Vocabulary
INSERT INTO vocabulary (language_id, word, pronunciation, translation, part_of_speech, difficulty_level, frequency_rank, example_sentences, cultural_context, tags)
SELECT 
    l.id,
    vocab.word,
    vocab.pronunciation,
    vocab.translation::jsonb,
    vocab.part_of_speech,
    vocab.difficulty_level,
    vocab.frequency_rank,
    vocab.example_sentences::jsonb,
    vocab.cultural_context,
    vocab.tags
FROM languages l
CROSS JOIN (
    VALUES 
    ('నమస్కారం', 'na-mas-kaa-ram', '{"en": "hello/goodbye", "es": "hola/adiós"}', 'interjection', 1, 1, '["నమస్కారం! ఎలా ఉన్నారు?", "నమస్కారం, మళ్లీ కలుద్దాం."]', 'Respectful greeting in Telugu culture', '{"greetings", "respect", "formal"}'),
    ('ధన్యవాదాలు', 'dhan-ya-vaa-daa-lu', '{"en": "thank you", "es": "gracias"}', 'interjection', 1, 2, '["చాలా ధన్యవాదాలు!", "మీ సహాయానికి ధన్యవాదాలు."]', 'Formal gratitude expression in Telugu', '{"politeness", "gratitude", "formal"}'),
    ('కుటుంబం', 'ku-tum-bam', '{"en": "family", "es": "familia"}', 'noun', 1, 20, '["కుటుంబం చాలా ముఖ్యం.", "మా కుటుంబం పెద్దది."]', 'Family is the foundation of Telugu culture', '{"family", "values", "culture"}'),
    ('సంస్కృతి', 'sam-skru-ti', '{"en": "culture", "es": "cultura"}', 'noun', 2, 100, '["తెలుగు సంస్కృతి గొప్పది.", "మన సంస్కృతిని కాపాడాలి."]', 'Telugu culture is rich with traditions and literature', '{"culture", "tradition", "heritage"}'),
    ('గౌరవం', 'gau-ra-vam', '{"en": "respect/honor", "es": "respeto/honor"}', 'noun', 2, 150, '["పెద్దలను గౌరవించాలి.", "గౌరవం ముఖ్యం."]', 'Respect and honor are core values in Telugu culture', '{"values", "respect", "honor"}'
) AS vocab(word, pronunciation, translation, part_of_speech, difficulty_level, frequency_rank, example_sentences, cultural_context, tags)
WHERE l.code = 'te'; 