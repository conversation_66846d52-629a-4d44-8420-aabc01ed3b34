import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface LessonRequest {
  topic: string
  language: string
  level: string
  difficulty: number
  lessonType: string
}

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string
      }>
    }
  }>
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
      },
    })
  }

  try {
    // Create Supabase client
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Get user from JWT
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      )
    }

    const { topic, language, level, difficulty, lessonType }: LessonRequest = await req.json()

    // Check cache first
    const cacheKey = `lesson:${topic}:${language}:${level}:${difficulty}:${lessonType}`
    const { data: cachedLesson } = await supabase
      .from('content_cache')
      .select('content_data')
      .eq('cache_key', cacheKey)
      .gt('expires_at', new Date().toISOString())
      .single()

    if (cachedLesson) {
      return new Response(
        JSON.stringify(cachedLesson.content_data),
        { headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Get language and level information
    const { data: languageData } = await supabase
      .from('languages')
      .select('id, name')
      .eq('code', language)
      .single()

    const { data: levelData } = await supabase
      .from('language_levels')
      .select('id, level_name, description')
      .eq('level_code', level)
      .eq('language_id', languageData?.id)
      .single()

    const { data: topicData } = await supabase
      .from('topics')
      .select('id, name, description')
      .ilike('name', `%${topic}%`)
      .single()

    // Generate lesson content with Gemini AI
    const prompt = `Create a ${level} level ${language} lesson about ${topic}.
    
Lesson Type: ${lessonType}
Difficulty: ${difficulty}/5
Target Level: ${levelData?.level_name} (${levelData?.description})

Please generate a comprehensive lesson with the following structure:
{
  "title": "Lesson title",
  "description": "Brief description",
  "content": {
    "introduction": "Introduction text",
    "vocabulary": [
      {"word": "example", "translation": "ejemplo", "pronunciation": "eh-HEM-ploh"}
    ],
    "dialogues": [
      {
        "speaker": "Person A",
        "text": "Example dialogue",
        "translation": "Translation"
      }
    ],
    "exercises": [
      {
        "type": "multiple_choice",
        "question": "Question text",
        "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
        "correct_answer": 0,
        "explanation": "Why this is correct"
      }
    ],
    "grammar_points": [
      {
        "rule": "Grammar rule",
        "explanation": "Detailed explanation",
        "examples": ["Example 1", "Example 2"]
      }
    ]
  },
  "metadata": {
    "estimated_duration": 15,
    "skill_focus": ["speaking", "vocabulary"],
    "cultural_notes": "Any cultural context"
  }
}`

    const geminiResponse = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${Deno.env.get('GEMINI_API_KEY')}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [{ text: prompt }]
            }
          ],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 2048,
          }
        })
      }
    )

    if (!geminiResponse.ok) {
      throw new Error(`Gemini API error: ${geminiResponse.statusText}`)
    }

    const geminiData: GeminiResponse = await geminiResponse.json()
    const generatedText = geminiData.candidates[0]?.content?.parts[0]?.text

    if (!generatedText) {
      throw new Error('No content generated from Gemini')
    }

    // Parse the JSON response from Gemini
    let lessonContent
    try {
      lessonContent = JSON.parse(generatedText)
    } catch (parseError) {
      // If JSON parsing fails, create a basic lesson structure
      lessonContent = {
        title: `${level} ${language} - ${topic}`,
        description: `A ${lessonType} lesson about ${topic}`,
        content: {
          introduction: generatedText.substring(0, 500),
          vocabulary: [],
          dialogues: [],
          exercises: [],
          grammar_points: []
        },
        metadata: {
          estimated_duration: 15,
          skill_focus: [lessonType],
          cultural_notes: ""
        }
      }
    }

    // Save lesson to database
    const { data: newLesson, error: lessonError } = await supabase
      .from('lessons')
      .insert([
        {
          title: lessonContent.title,
          description: lessonContent.description,
          content: lessonContent.content,
          language_id: languageData?.id,
          level_id: levelData?.id,
          topic_id: topicData?.id,
          difficulty_level: difficulty,
          lesson_type: lessonType,
          estimated_duration_minutes: lessonContent.metadata?.estimated_duration || 15,
          is_published: true,
          metadata: lessonContent.metadata || {}
        }
      ])
      .select()
      .single()

    if (lessonError) {
      console.error('Error saving lesson:', lessonError)
    }

    // Cache the result for 1 hour
    const expiresAt = new Date()
    expiresAt.setHours(expiresAt.getHours() + 1)

    await supabase
      .from('content_cache')
      .upsert([
        {
          cache_key: cacheKey,
          content_data: lessonContent,
          content_type: 'lesson',
          expires_at: expiresAt.toISOString()
        }
      ])

    // Generate embeddings for vector search (optional)
    if (newLesson) {
      try {
        const embeddingText = `${lessonContent.title} ${lessonContent.description} ${JSON.stringify(lessonContent.content.vocabulary)} ${JSON.stringify(lessonContent.content.grammar_points)}`
        
        const embeddingResponse = await fetch(
          'https://api.openai.com/v1/embeddings',
          {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${Deno.env.get('OPENAI_API_KEY')}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              model: 'text-embedding-ada-002',
              input: embeddingText
            })
          }
        )

        if (embeddingResponse.ok) {
          const embeddingData = await embeddingResponse.json()
          const embedding = embeddingData.data[0]?.embedding

          if (embedding) {
            await supabase
              .from('lesson_embeddings')
              .insert([
                {
                  lesson_id: newLesson.id,
                  content_text: embeddingText,
                  embedding: embedding,
                  metadata: { generated_at: new Date().toISOString() }
                }
              ])
          }
        }
      } catch (embeddingError) {
        console.error('Error generating embeddings:', embeddingError)
        // Don't fail the whole request if embeddings fail
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        lesson: lessonContent,
        lesson_id: newLesson?.id
      }),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    )

  } catch (error) {
    console.error('Error generating lesson:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to generate lesson',
        details: error.message 
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    )
  }
}) 