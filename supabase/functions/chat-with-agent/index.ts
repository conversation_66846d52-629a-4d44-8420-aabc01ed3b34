import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface ChatRequest {
  conversation_id?: string
  agent_id: string
  lesson_id?: string
  message: string
  message_type?: string
}

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string
      }>
    }
  }>
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
      },
    })
  }

  try {
    // Create Supabase client
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Get user from JWT
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      )
    }

    const { conversation_id, agent_id, lesson_id, message, message_type = 'text' }: ChatRequest = await req.json()

    // Get agent information
    const { data: agent, error: agentError } = await supabase
      .from('ai_agents')
      .select('*')
      .eq('id', agent_id)
      .eq('is_active', true)
      .single()

    if (agentError || !agent) {
      return new Response(
        JSON.stringify({ error: 'Agent not found or inactive' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Get or create conversation
    let conversation
    if (conversation_id) {
      const { data: existingConversation, error: convError } = await supabase
        .from('chat_conversations')
        .select('*')
        .eq('id', conversation_id)
        .eq('user_id', user.id)
        .single()

      if (convError || !existingConversation) {
        return new Response(
          JSON.stringify({ error: 'Conversation not found' }),
          { status: 404, headers: { 'Content-Type': 'application/json' } }
        )
      }
      conversation = existingConversation
    } else {
      // Create new conversation
      const { data: newConversation, error: newConvError } = await supabase
        .from('chat_conversations')
        .insert([
          {
            user_id: user.id,
            agent_id: agent_id,
            lesson_id: lesson_id,
            title: `Chat with ${agent.name}`,
            status: 'active'
          }
        ])
        .select()
        .single()

      if (newConvError || !newConversation) {
        return new Response(
          JSON.stringify({ error: 'Failed to create conversation' }),
          { status: 500, headers: { 'Content-Type': 'application/json' } }
        )
      }
      conversation = newConversation
    }

    // Save user message
    const { data: userMessage, error: userMessageError } = await supabase
      .from('chat_messages')
      .insert([
        {
          conversation_id: conversation.id,
          sender_type: 'user',
          content: message,
          message_type: message_type
        }
      ])
      .select()
      .single()

    if (userMessageError) {
      console.error('Error saving user message:', userMessageError)
    }

    // Get conversation history for context
    const { data: messageHistory } = await supabase
      .from('chat_messages')
      .select('sender_type, content, created_at')
      .eq('conversation_id', conversation.id)
      .order('created_at', { ascending: true })
      .limit(20) // Last 20 messages for context

    // Get lesson context if available
    let lessonContext = ''
    if (lesson_id) {
      const { data: lesson } = await supabase
        .from('lessons')
        .select('title, description, content, language_id, level_id')
        .eq('id', lesson_id)
        .single()

      if (lesson) {
        const { data: language } = await supabase
          .from('languages')
          .select('name, code')
          .eq('id', lesson.language_id)
          .single()

        const { data: level } = await supabase
          .from('language_levels')
          .select('level_name, description')
          .eq('id', lesson.level_id)
          .single()

        lessonContext = `
Current Lesson Context:
- Language: ${language?.name} (${language?.code})
- Level: ${level?.level_name} - ${level?.description}
- Lesson: ${lesson.title}
- Description: ${lesson.description}
- Current vocabulary: ${JSON.stringify(lesson.content?.vocabulary || [])}
- Grammar points: ${JSON.stringify(lesson.content?.grammar_points || [])}
`
      }
    }

    // Build conversation history for AI context
    const conversationHistory = messageHistory?.map(msg => 
      `${msg.sender_type === 'user' ? 'User' : agent.name}: ${msg.content}`
    ).join('\n') || ''

    // Create AI prompt
    const aiPrompt = `${agent.system_prompt}

${lessonContext}

Agent Personality: ${JSON.stringify(agent.personality_traits)}
Specializations: ${agent.specializations.join(', ')}

Recent Conversation History:
${conversationHistory}

User's latest message: ${message}

Please respond as ${agent.name} in character. Keep responses helpful, engaging, and appropriate for language learning. If this is related to a specific lesson, reference the lesson content when relevant.`

    // Generate AI response
    const geminiResponse = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${Deno.env.get('GEMINI_API_KEY')}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [{ text: aiPrompt }]
            }
          ],
          generationConfig: {
            temperature: 0.8,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024,
          }
        })
      }
    )

    if (!geminiResponse.ok) {
      throw new Error(`Gemini API error: ${geminiResponse.statusText}`)
    }

    const geminiData: GeminiResponse = await geminiResponse.json()
    const aiResponseText = geminiData.candidates[0]?.content?.parts[0]?.text

    if (!aiResponseText) {
      throw new Error('No response generated from AI')
    }

    // Save AI response
    const { data: aiMessage, error: aiMessageError } = await supabase
      .from('chat_messages')
      .insert([
        {
          conversation_id: conversation.id,
          sender_type: 'agent',
          content: aiResponseText,
          message_type: 'text',
          metadata: {
            agent_id: agent.id,
            agent_name: agent.name,
            generated_at: new Date().toISOString()
          }
        }
      ])
      .select()
      .single()

    if (aiMessageError) {
      console.error('Error saving AI message:', aiMessageError)
    }

    // Update conversation timestamp
    await supabase
      .from('chat_conversations')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', conversation.id)

    // Broadcast real-time update
    const channel = supabase.channel(`conversation:${conversation.id}`)
    await channel.send({
      type: 'broadcast',
      event: 'new_message',
      payload: {
        message: aiMessage,
        conversation_id: conversation.id
      }
    })

    return new Response(
      JSON.stringify({
        success: true,
        conversation_id: conversation.id,
        message: aiMessage,
        agent: {
          id: agent.id,
          name: agent.name,
          avatar_url: agent.avatar_url
        }
      }),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    )

  } catch (error) {
    console.error('Error in chat:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to process chat message',
        details: error.message 
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    )
  }
}) 