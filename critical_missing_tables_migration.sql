-- NIRA Critical Missing Tables Migration
-- This script creates the most critical missing tables for UI functionality

-- =====================================================
-- 1. ACHIEVEMENTS TABLE (Critical for Gamification)
-- =====================================================

CREATE TABLE achievements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    category VARCHAR(50) NOT NULL,
    points INTEGER DEFAULT 0,
    difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level BETWEEN 1 AND 6),
    requirements JSONB DEFAULT '{}',
    unlock_criteria JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for achievements
CREATE INDEX idx_achievements_category ON achievements(category);
CREATE INDEX idx_achievements_difficulty ON achievements(difficulty_level);
CREATE INDEX idx_achievements_active ON achievements(is_active);

-- =====================================================
-- 2. DAILY GOALS TABLE (Critical for Home Dashboard)
-- =====================================================

CREATE TABLE daily_goals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    goal_date DATE NOT NULL,
    target_minutes INTEGER DEFAULT 15,
    target_lessons INTEGER DEFAULT 1,
    target_vocabulary INTEGER DEFAULT 5,
    target_exercises INTEGER DEFAULT 3,
    actual_minutes INTEGER DEFAULT 0,
    actual_lessons INTEGER DEFAULT 0,
    actual_vocabulary INTEGER DEFAULT 0,
    actual_exercises INTEGER DEFAULT 0,
    is_completed BOOLEAN DEFAULT false,
    completion_percentage DECIMAL(5,2) DEFAULT 0.0,
    bonus_points INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, goal_date)
);

-- Create indexes for daily goals
CREATE INDEX idx_daily_goals_user_date ON daily_goals(user_id, goal_date);
CREATE INDEX idx_daily_goals_completion ON daily_goals(is_completed);
CREATE INDEX idx_daily_goals_date ON daily_goals(goal_date);

-- =====================================================
-- 3. USER STREAKS TABLE (Critical for Engagement)
-- =====================================================

CREATE TABLE user_streaks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    language_id UUID REFERENCES languages(id),
    streak_type VARCHAR(50) DEFAULT 'daily' CHECK (streak_type IN ('daily', 'weekly', 'lesson', 'vocabulary')),
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    last_activity_date DATE,
    streak_start_date DATE,
    total_activities INTEGER DEFAULT 0,
    freeze_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, language_id, streak_type)
);

-- Create indexes for user streaks
CREATE INDEX idx_user_streaks_user ON user_streaks(user_id);
CREATE INDEX idx_user_streaks_language ON user_streaks(language_id);
CREATE INDEX idx_user_streaks_type ON user_streaks(streak_type);

-- =====================================================
-- 4. USER PREFERENCES TABLE (Critical for Personalization)
-- =====================================================

CREATE TABLE user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- UI Preferences
    theme VARCHAR(20) DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
    language_interface VARCHAR(10) DEFAULT 'en',
    font_size VARCHAR(20) DEFAULT 'medium' CHECK (font_size IN ('small', 'medium', 'large')),
    
    -- Notification Preferences
    notifications_enabled BOOLEAN DEFAULT true,
    daily_reminder_time TIME DEFAULT '19:00:00',
    study_reminder_enabled BOOLEAN DEFAULT true,
    achievement_notifications BOOLEAN DEFAULT true,
    streak_reminders BOOLEAN DEFAULT true,
    
    -- Audio & Interaction Preferences
    sound_effects_enabled BOOLEAN DEFAULT true,
    haptic_feedback_enabled BOOLEAN DEFAULT true,
    auto_play_audio BOOLEAN DEFAULT true,
    speech_rate DECIMAL(3,2) DEFAULT 1.0,
    
    -- Learning Preferences
    difficulty_preference VARCHAR(20) DEFAULT 'adaptive' CHECK (difficulty_preference IN ('easy', 'normal', 'hard', 'adaptive')),
    learning_pace VARCHAR(20) DEFAULT 'normal' CHECK (learning_pace IN ('slow', 'normal', 'fast')),
    review_frequency VARCHAR(20) DEFAULT 'daily' CHECK (review_frequency IN ('daily', 'every_other_day', 'weekly')),
    
    -- Privacy Preferences
    data_sharing_enabled BOOLEAN DEFAULT false,
    analytics_enabled BOOLEAN DEFAULT true,
    leaderboard_visible BOOLEAN DEFAULT true,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Create index for user preferences
CREATE INDEX idx_user_preferences_user ON user_preferences(user_id);

-- =====================================================
-- 5. SPACED REPETITION TABLE (Critical for Vocabulary)
-- =====================================================

CREATE TABLE spaced_repetition (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    vocabulary_id UUID NOT NULL REFERENCES vocabulary(id) ON DELETE CASCADE,
    
    -- Spaced Repetition Algorithm Data
    ease_factor DECIMAL(4,2) DEFAULT 2.5 CHECK (ease_factor >= 1.3),
    interval_days INTEGER DEFAULT 1 CHECK (interval_days > 0),
    repetitions INTEGER DEFAULT 0,
    next_review_date DATE NOT NULL,
    last_reviewed_at TIMESTAMP WITH TIME ZONE,
    
    -- Performance Tracking
    total_reviews INTEGER DEFAULT 0,
    correct_reviews INTEGER DEFAULT 0,
    accuracy_percentage DECIMAL(5,2) DEFAULT 0.0,
    average_response_time INTEGER DEFAULT 0, -- milliseconds
    
    -- Recent Performance (last 5 reviews)
    quality_responses JSONB DEFAULT '[]',
    response_times JSONB DEFAULT '[]',
    
    -- Status
    mastery_level INTEGER DEFAULT 0 CHECK (mastery_level BETWEEN 0 AND 5),
    is_learning BOOLEAN DEFAULT true,
    is_mature BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, vocabulary_id)
);

-- Create indexes for spaced repetition
CREATE INDEX idx_spaced_repetition_user ON spaced_repetition(user_id);
CREATE INDEX idx_spaced_repetition_vocabulary ON spaced_repetition(vocabulary_id);
CREATE INDEX idx_spaced_repetition_review_date ON spaced_repetition(next_review_date);
CREATE INDEX idx_spaced_repetition_user_review ON spaced_repetition(user_id, next_review_date);

-- =====================================================
-- 6. USER STUDY SESSIONS TABLE (Critical for Analytics)
-- =====================================================

CREATE TABLE user_study_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    language_id UUID REFERENCES languages(id),
    session_type VARCHAR(50) DEFAULT 'lesson' CHECK (session_type IN ('lesson', 'vocabulary', 'conversation', 'exercise', 'review')),
    
    -- Session Data
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE,
    duration_minutes INTEGER,
    
    -- Activity Tracking
    lessons_completed INTEGER DEFAULT 0,
    exercises_completed INTEGER DEFAULT 0,
    vocabulary_reviewed INTEGER DEFAULT 0,
    conversations_held INTEGER DEFAULT 0,
    
    -- Performance
    total_points_earned INTEGER DEFAULT 0,
    accuracy_percentage DECIMAL(5,2),
    mistakes_made INTEGER DEFAULT 0,
    
    -- Context
    device_type VARCHAR(50),
    app_version VARCHAR(20),
    session_metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for study sessions
CREATE INDEX idx_study_sessions_user ON user_study_sessions(user_id);
CREATE INDEX idx_study_sessions_language ON user_study_sessions(language_id);
CREATE INDEX idx_study_sessions_date ON user_study_sessions(started_at);
CREATE INDEX idx_study_sessions_type ON user_study_sessions(session_type);

-- =====================================================
-- 7. EXERCISE ATTEMPTS TABLE (Critical for Progress Tracking)
-- =====================================================

CREATE TABLE exercise_attempts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    exercise_id UUID NOT NULL REFERENCES exercises(id) ON DELETE CASCADE,
    session_id UUID REFERENCES user_study_sessions(id),
    
    -- Attempt Data
    attempt_number INTEGER DEFAULT 1,
    user_answer JSONB,
    correct_answer JSONB,
    is_correct BOOLEAN,
    points_earned INTEGER DEFAULT 0,
    time_taken_ms INTEGER,
    
    -- Feedback
    hint_used BOOLEAN DEFAULT false,
    hints_count INTEGER DEFAULT 0,
    feedback_shown BOOLEAN DEFAULT false,
    
    -- Context
    difficulty_level INTEGER,
    exercise_metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for exercise attempts
CREATE INDEX idx_exercise_attempts_user ON exercise_attempts(user_id);
CREATE INDEX idx_exercise_attempts_exercise ON exercise_attempts(exercise_id);
CREATE INDEX idx_exercise_attempts_session ON exercise_attempts(session_id);
CREATE INDEX idx_exercise_attempts_date ON exercise_attempts(created_at);

-- =====================================================
-- 8. NOTIFICATIONS TABLE (Critical for User Engagement)
-- =====================================================

CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Notification Content
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    notification_type VARCHAR(50) NOT NULL CHECK (notification_type IN ('achievement', 'streak', 'reminder', 'social', 'system', 'lesson')),
    
    -- Delivery
    is_read BOOLEAN DEFAULT false,
    is_sent BOOLEAN DEFAULT false,
    scheduled_for TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    action_url TEXT,
    icon VARCHAR(100),
    priority INTEGER DEFAULT 1 CHECK (priority BETWEEN 1 AND 5),
    metadata JSONB DEFAULT '{}',
    
    -- Expiry
    expires_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for notifications
CREATE INDEX idx_notifications_user ON notifications(user_id);
CREATE INDEX idx_notifications_unread ON notifications(user_id, is_read) WHERE is_read = false;
CREATE INDEX idx_notifications_scheduled ON notifications(scheduled_for) WHERE scheduled_for IS NOT NULL;
CREATE INDEX idx_notifications_type ON notifications(notification_type);

-- =====================================================
-- 9. ADD MISSING COLUMNS TO USERS TABLE
-- =====================================================

-- Add missing user profile columns
ALTER TABLE users ADD COLUMN IF NOT EXISTS first_name VARCHAR(100);
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_name VARCHAR(100);
ALTER TABLE users ADD COLUMN IF NOT EXISTS avatar_url TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS bio TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS date_of_birth DATE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS country VARCHAR(100);

-- Add missing engagement columns
ALTER TABLE users ADD COLUMN IF NOT EXISTS longest_streak INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_active_date TIMESTAMP WITH TIME ZONE DEFAULT NOW();
ALTER TABLE users ADD COLUMN IF NOT EXISTS total_study_time_minutes INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS level INTEGER DEFAULT 1;
ALTER TABLE users ADD COLUMN IF NOT EXISTS badges_earned INTEGER DEFAULT 0;

-- Add missing subscription columns
ALTER TABLE users ADD COLUMN IF NOT EXISTS subscription_tier VARCHAR(50) DEFAULT 'free';
ALTER TABLE users ADD COLUMN IF NOT EXISTS subscription_expires_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS trial_ends_at TIMESTAMP WITH TIME ZONE;

-- =====================================================
-- 10. CREATE TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Update timestamps trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables with updated_at columns
CREATE TRIGGER update_achievements_updated_at BEFORE UPDATE ON achievements FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_daily_goals_updated_at BEFORE UPDATE ON daily_goals FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_streaks_updated_at BEFORE UPDATE ON user_streaks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_preferences_updated_at BEFORE UPDATE ON user_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_spaced_repetition_updated_at BEFORE UPDATE ON spaced_repetition FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

-- Insert some default achievements
INSERT INTO achievements (name, description, icon, category, points, difficulty_level, requirements) VALUES
('First Steps', 'Complete your first lesson', '🎯', 'learning', 10, 1, '{"lessons_completed": 1}'),
('Vocabulary Builder', 'Learn 10 new words', '📚', 'vocabulary', 25, 1, '{"vocabulary_learned": 10}'),
('Streak Starter', 'Maintain a 3-day learning streak', '🔥', 'engagement', 50, 2, '{"streak_days": 3}'),
('Conversation Rookie', 'Have your first AI conversation', '💬', 'conversation', 20, 1, '{"conversations": 1}'),
('Dedicated Learner', 'Study for 7 days in a row', '⭐', 'engagement', 100, 3, '{"streak_days": 7}');

COMMENT ON TABLE achievements IS 'Achievement definitions for gamification system';
COMMENT ON TABLE daily_goals IS 'Daily learning goals and progress tracking';
COMMENT ON TABLE user_streaks IS 'User learning streaks by language and type';
COMMENT ON TABLE user_preferences IS 'User interface and learning preferences';
COMMENT ON TABLE spaced_repetition IS 'Spaced repetition algorithm data for vocabulary';
COMMENT ON TABLE user_study_sessions IS 'User study session tracking and analytics';
COMMENT ON TABLE exercise_attempts IS 'Individual exercise attempt tracking';
COMMENT ON TABLE notifications IS 'User notification system';
