import Foundation
import Supabase
import SwiftUI

// MARK: - Database Models matching Supabase schema

// MARK: - User Models

struct SupabaseUserProfile: Codable, Identifiable {
    let id: String
    let email: String
    let firstName: String?
    let lastName: String?
    let preferredLanguages: [String]
    let createdAt: Date
    var lastActiveDate: Date
    let isEmailVerified: Bool
    let subscriptionTier: String
    let totalLessonsCompleted: Int
    let currentStreak: Int
    let longestStreak: Int
    let totalStudyTimeMinutes: Int

    enum CodingKeys: String, CodingKey {
        case id, email
        case firstName = "first_name"
        case lastName = "last_name"
        case preferredLanguages = "preferred_languages"
        case createdAt = "created_at"
        case lastActiveDate = "last_active_date"
        case isEmailVerified = "is_email_verified"
        case subscriptionTier = "subscription_tier"
        case totalLessonsCompleted = "total_lessons_completed"
        case currentStreak = "current_streak"
        case longestStreak = "longest_streak"
        case totalStudyTimeMinutes = "total_study_time_minutes"
    }
}

// MARK: - Language Models

struct SupabaseLanguageModel: Codable, Identifiable {
    let id: UUID
    let code: String
    let name: String
    let nativeName: String
    let isActive: Bool
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id, code, name
        case nativeName = "native_name"
        case isActive = "is_active"
        case createdAt = "created_at"
    }
}

struct SupabaseLanguageLevel: Codable, Identifiable {
    let id: UUID
    let languageId: UUID
    let levelCode: String
    let levelName: String
    let description: String?
    let orderIndex: Int
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case languageId = "language_id"
        case levelCode = "level_code"
        case levelName = "level_name"
        case description
        case orderIndex = "order_index"
        case createdAt = "created_at"
    }
}

// MARK: - Topic Models

struct SupabaseTopic: Codable, Identifiable {
    let id: UUID
    let name: String
    let description: String?
    let iconName: String?
    let colorHex: String?
    let isActive: Bool
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id, name, description
        case iconName = "icon_name"
        case colorHex = "color_hex"
        case isActive = "is_active"
        case createdAt = "created_at"
    }
}

// MARK: - Learning Path Models

struct SupabaseLearningPath: Codable, Identifiable {
    let id: UUID
    let languageId: UUID
    let agentId: UUID
    let name: String
    let description: String
    let level: String
    let estimatedHours: Int
    let sequenceOrder: Int
    let prerequisites: [String]?
    let learningObjectives: [String]?
    let culturalFocus: [String]?
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id, name, description, level, prerequisites
        case languageId = "language_id"
        case agentId = "agent_id"
        case estimatedHours = "estimated_hours"
        case sequenceOrder = "sequence_order"
        case learningObjectives = "learning_objectives"
        case culturalFocus = "cultural_focus"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Lesson Models

struct SupabaseLesson: Codable, Identifiable {
    let id: UUID
    let pathId: UUID
    let title: String
    let description: String?
    let lessonType: String
    let difficultyLevel: Int?
    let estimatedDuration: Int?
    let sequenceOrder: Int?
    let learningObjectives: [String]?
    let vocabularyFocus: [String]?
    let grammarConcepts: [String]?
    let culturalNotes: String?
    let prerequisiteLessons: [String]?
    let contentMetadata: SupabaseAnyCodable?
    let isActive: Bool?
    let createdAt: Date?
    let updatedAt: Date?

    // Audio support
    let audioUrl: String?
    let hasAudio: Bool?
    let audioMetadata: SupabaseAnyCodable?

    enum CodingKeys: String, CodingKey {
        case id, title, description
        case pathId = "path_id"
        case lessonType = "lesson_type"
        case difficultyLevel = "difficulty_level"
        case estimatedDuration = "estimated_duration"
        case sequenceOrder = "sequence_order"
        case learningObjectives = "learning_objectives"
        case vocabularyFocus = "vocabulary_focus"
        case grammarConcepts = "grammar_concepts"
        case culturalNotes = "cultural_notes"
        case prerequisiteLessons = "prerequisite_lessons"
        case contentMetadata = "content_metadata"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case audioUrl = "audio_url"
        case hasAudio = "has_audio"
        case audioMetadata = "audio_metadata"
    }

    // Computed properties for UI
    var difficultyText: String {
        switch difficultyLevel ?? 1 {
        case 1: return "A1"
        case 2: return "A2"
        case 3: return "B1"
        case 4: return "B2"
        case 5: return "C1"
        case 6: return "C2"
        default: return "A1"
        }
    }

    var difficultyColor: Color {
        switch difficultyLevel ?? 1 {
        case 1, 2: return .green
        case 3, 4: return .orange
        case 5, 6: return .red
        default: return .green
        }
    }

    var formattedDuration: String {
        let duration = estimatedDuration ?? 15
        let hours = duration / 60
        let minutes = duration % 60

        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }

    // For backward compatibility with existing UI code
    var content: String {
        return description ?? ""
    }

    var languageName: String? {
        return nil // Will be populated by the service
    }

    var category: String? {
        return lessonType
    }

    var topics: [String] {
        return vocabularyFocus ?? []
    }

    var vocabulary: [SupabaseVocabularyItem] {
        return [] // Will be populated separately if needed
    }

    var exercises: [SupabaseExercise] {
        return [] // Will be populated separately if needed
    }

    var culturalContext: String? {
        return culturalNotes
    }
}

struct SupabaseVocabularyItem: Codable, Identifiable {
    let id: UUID
    let word: String
    let translation: String
    let partOfSpeech: String?
    let context: String?
    let difficulty: String?
    let pronunciation: String?
    let example: String?
    let exampleTranslation: String?

    enum CodingKeys: String, CodingKey {
        case id, word, translation, context, difficulty, pronunciation, example
        case partOfSpeech = "part_of_speech"
        case exampleTranslation = "example_translation"
    }

    init(id: UUID = UUID(), word: String, translation: String, partOfSpeech: String? = nil, context: String? = nil, difficulty: String? = nil, pronunciation: String? = nil, example: String? = nil, exampleTranslation: String? = nil) {
        self.id = id
        self.word = word
        self.translation = translation
        self.partOfSpeech = partOfSpeech
        self.context = context
        self.difficulty = difficulty
        self.pronunciation = pronunciation
        self.example = example
        self.exampleTranslation = exampleTranslation
    }
}

struct SupabaseExercise: Codable, Identifiable {
    let id: UUID
    let type: String
    let question: String
    let options: [String]?
    let correctAnswer: String
    let explanation: String?
    let points: Int
    let difficulty: Int

    enum CodingKeys: String, CodingKey {
        case id, type, question, options, explanation, points, difficulty
        case correctAnswer = "correct_answer"
    }
}

// MARK: - Progress Models

struct SupabaseUserProgress: Codable, Identifiable {
    let id: UUID
    let userId: String
    let lessonId: UUID
    let status: String
    let score: Int?
    let timeSpentSeconds: Int
    let completedAt: Date?
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id, status, score
        case userId = "user_id"
        case lessonId = "lesson_id"
        case timeSpentSeconds = "time_spent_seconds"
        case completedAt = "completed_at"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Learning Session Models

struct SupabaseLearningSession: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let lessonId: UUID
    let sessionType: SupabaseSessionType
    let startTime: Date
    let endTime: Date?
    let durationSeconds: Int?
    let interactionsData: SupabaseAnyCodable
    let performanceMetrics: SupabaseAnyCodable
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case lessonId = "lesson_id"
        case sessionType = "session_type"
        case startTime = "start_time"
        case endTime = "end_time"
        case durationSeconds = "duration_seconds"
        case interactionsData = "interactions_data"
        case performanceMetrics = "performance_metrics"
        case createdAt = "created_at"
    }
}

enum SupabaseSessionType: String, Codable, CaseIterable {
    case practice = "practice"
    case review = "review"
    case test = "test"

    var displayName: String {
        switch self {
        case .practice: return "Practice"
        case .review: return "Review"
        case .test: return "Test"
        }
    }
}

// MARK: - AI Agent Models

struct SupabaseAIAgent: Codable, Identifiable {
    let id: UUID
    let name: String
    let description: String?
    let personalityTraits: SupabaseAnyCodable
    let languageId: UUID
    let specializations: [String]
    let systemPrompt: String
    let avatarUrl: String?
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id, name, description
        case personalityTraits = "personality_traits"
        case languageId = "language_id"
        case specializations
        case systemPrompt = "system_prompt"
        case avatarUrl = "avatar_url"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Chat Models

struct SupabaseChatConversation: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let agentId: UUID
    let lessonId: UUID?
    let title: String?
    let status: SupabaseConversationStatus
    let metadata: SupabaseAnyCodable
    let createdAt: Date
    let updatedAt: Date

    // Optional relationship data
    var agent: SupabaseAIAgent?
    var lesson: SupabaseLesson?

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case agentId = "agent_id"
        case lessonId = "lesson_id"
        case title, status, metadata
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case agent, lesson
    }
}

enum SupabaseConversationStatus: String, Codable, CaseIterable {
    case active = "active"
    case completed = "completed"
    case archived = "archived"
    case deleted = "deleted"

    var displayName: String {
        switch self {
        case .active: return "Active"
        case .completed: return "Completed"
        case .archived: return "Archived"
        case .deleted: return "Deleted"
        }
    }
}

struct SupabaseChatMessage: Codable, Identifiable {
    let id: UUID
    let conversationId: UUID
    let senderType: SupabaseSenderType
    let content: String
    let messageType: SupabaseMessageType
    let metadata: SupabaseAnyCodable
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case conversationId = "conversation_id"
        case senderType = "sender_type"
        case content
        case messageType = "message_type"
        case metadata
        case createdAt = "created_at"
    }
}

enum SupabaseSenderType: String, Codable, CaseIterable {
    case user = "user"
    case agent = "agent"

    var displayName: String {
        switch self {
        case .user: return "You"
        case .agent: return "AI"
        }
    }
}

enum SupabaseMessageType: String, Codable, CaseIterable {
    case text = "text"
    case audio = "audio"
    case image = "image"
    case lessonContent = "lesson_content"

    var displayName: String {
        switch self {
        case .text: return "Text"
        case .audio: return "Audio"
        case .image: return "Image"
        case .lessonContent: return "Lesson"
        }
    }
}

// MARK: - Vector Search Models

struct SupabaseLessonEmbedding: Codable, Identifiable {
    let id: UUID
    let lessonId: UUID
    let contentText: String
    let embedding: [Double]  // Vector embedding
    let metadata: SupabaseAnyCodable
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case lessonId = "lesson_id"
        case contentText = "content_text"
        case embedding, metadata
        case createdAt = "created_at"
    }
}

// MARK: - Cache Models

struct SupabaseContentCache: Codable, Identifiable {
    let id: UUID
    let cacheKey: String
    let contentData: SupabaseAnyCodable
    let contentType: String
    let expiresAt: Date?
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case cacheKey = "cache_key"
        case contentData = "content_data"
        case contentType = "content_type"
        case expiresAt = "expires_at"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Helper Types

// Type-erased codable for JSON fields - defined in SupabaseClient.swift

// MARK: - Extensions for UI

extension SupabaseUserProfile {
    var fullName: String {
        "\(firstName ?? "") \(lastName ?? "")"
    }

    var initials: String {
        let first = firstName?.prefix(1).uppercased() ?? ""
        let last = lastName?.prefix(1).uppercased() ?? ""
        return "\(first)\(last)"
    }
}

extension SupabaseLesson {
    var difficultyStars: String {
        String(repeating: "⭐", count: difficultyLevel ?? 1)
    }
}

extension SupabaseUserProgress {
    var progressPercentage: Int {
        switch status {
        case "completed": return 100
        case "in_progress": return 50
        case "started": return 25
        default: return 0
        }
    }

    var formattedTimeSpent: String {
        let hours = timeSpentSeconds / 3600
        let minutes = (timeSpentSeconds % 3600) / 60

        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
}

extension SupabaseChatMessage {
    var isFromUser: Bool {
        senderType == .user
    }

    var isFromAgent: Bool {
        senderType == .agent
    }

    var formattedTimestamp: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: createdAt)
    }
}

// MARK: - Type Aliases for backward compatibility

typealias UserProfile = SupabaseUserProfile
typealias LanguageModel = SupabaseLanguageModel
typealias LanguageLevel = SupabaseLanguageLevel
typealias Topic = SupabaseTopic
typealias UserProgress = SupabaseUserProgress
typealias AIAgent = SupabaseAIAgent
typealias ChatConversation = SupabaseChatConversation
typealias ChatMessage = SupabaseChatMessage
typealias LessonEmbedding = SupabaseLessonEmbedding
typealias ContentCache = SupabaseContentCache

// MARK: - Enhanced Knowledge Base Models

struct SupabaseKnowledgeBase: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let agentId: UUID?
    let language: String
    let fileName: String
    let fileType: String
    let fileSize: Int64
    let filePath: String
    let title: String?
    let description: String?
    let content: String?
    let extractedText: String?
    let metadata: SupabaseAnyCodable
    let tags: [String]
    let isProcessed: Bool
    let processingStatus: ProcessingStatus
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case agentId = "agent_id"
        case language
        case fileName = "file_name"
        case fileType = "file_type"
        case fileSize = "file_size"
        case filePath = "file_path"
        case title, description, content
        case extractedText = "extracted_text"
        case metadata, tags
        case isProcessed = "is_processed"
        case processingStatus = "processing_status"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

enum ProcessingStatus: String, Codable, CaseIterable {
    case pending = "pending"
    case processing = "processing"
    case completed = "completed"
    case failed = "failed"

    var displayName: String {
        switch self {
        case .pending: return "Pending"
        case .processing: return "Processing"
        case .completed: return "Completed"
        case .failed: return "Failed"
        }
    }

    var color: Color {
        switch self {
        case .pending: return .orange
        case .processing: return .blue
        case .completed: return .green
        case .failed: return .red
        }
    }
}

// MARK: - Enhanced Conversation Models

struct SupabaseEnhancedConversation: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let agentId: UUID
    let agentName: String
    let language: String
    let title: String?
    let status: SupabaseConversationStatus
    let messageCount: Int
    let lastMessageAt: Date?
    let knowledgeBaseIds: [UUID]
    let metadata: SupabaseAnyCodable
    let createdAt: Date
    let updatedAt: Date

    // Optional relationship data
    var agent: SupabaseAIAgent?
    var messages: [SupabaseEnhancedMessage]?
    var knowledgeBase: [SupabaseKnowledgeBase]?

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case agentId = "agent_id"
        case agentName = "agent_name"
        case language, title, status
        case messageCount = "message_count"
        case lastMessageAt = "last_message_at"
        case knowledgeBaseIds = "knowledge_base_ids"
        case metadata
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case agent, messages, knowledgeBase
    }
}

struct SupabaseEnhancedMessage: Codable, Identifiable {
    let id: UUID
    let conversationId: UUID
    let senderType: SupabaseSenderType
    let content: String
    let messageType: SupabaseMessageType
    let attachments: [SupabaseMessageAttachment]
    let voiceData: SupabaseVoiceData?
    let aiMetadata: SupabaseAIMetadata?
    let responseTime: Double?
    let confidence: Double?
    let grammarCorrections: [String]
    let culturalNotes: [String]
    let vocabularyHighlights: [SupabaseVocabularyHighlight]
    let metadata: SupabaseAnyCodable
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case conversationId = "conversation_id"
        case senderType = "sender_type"
        case content
        case messageType = "message_type"
        case attachments
        case voiceData = "voice_data"
        case aiMetadata = "ai_metadata"
        case responseTime = "response_time"
        case confidence
        case grammarCorrections = "grammar_corrections"
        case culturalNotes = "cultural_notes"
        case vocabularyHighlights = "vocabulary_highlights"
        case metadata
        case createdAt = "created_at"
    }
}

struct SupabaseMessageAttachment: Codable, Identifiable {
    let id: UUID
    let messageId: UUID
    let fileName: String
    let fileType: String
    let fileSize: Int64
    let filePath: String
    let thumbnailPath: String?
    let isProcessed: Bool
    let extractedText: String?
    let metadata: SupabaseAnyCodable
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case messageId = "message_id"
        case fileName = "file_name"
        case fileType = "file_type"
        case fileSize = "file_size"
        case filePath = "file_path"
        case thumbnailPath = "thumbnail_path"
        case isProcessed = "is_processed"
        case extractedText = "extracted_text"
        case metadata
        case createdAt = "created_at"
    }
}

struct SupabaseVoiceData: Codable {
    let audioPath: String
    let duration: Double
    let transcription: String?
    let language: String?
    let confidence: Double?
    let isProcessed: Bool

    enum CodingKeys: String, CodingKey {
        case audioPath = "audio_path"
        case duration, transcription, language, confidence
        case isProcessed = "is_processed"
    }
}

struct SupabaseAIMetadata: Codable {
    let model: String
    let temperature: Double?
    let maxTokens: Int?
    let promptTokens: Int?
    let completionTokens: Int?
    let totalTokens: Int?
    let finishReason: String?
    let processingTime: Double?

    enum CodingKeys: String, CodingKey {
        case model, temperature
        case maxTokens = "max_tokens"
        case promptTokens = "prompt_tokens"
        case completionTokens = "completion_tokens"
        case totalTokens = "total_tokens"
        case finishReason = "finish_reason"
        case processingTime = "processing_time"
    }
}

struct SupabaseVocabularyHighlight: Codable, Identifiable {
    let id: UUID
    let word: String
    let definition: String
    let partOfSpeech: String?
    let difficulty: String?
    let language: String
    let culturalContext: String?

    enum CodingKeys: String, CodingKey {
        case id, word, definition
        case partOfSpeech = "part_of_speech"
        case difficulty, language
        case culturalContext = "cultural_context"
    }
}

// MARK: - Simulation Models

struct SupabaseSimulationPersona: Codable, Identifiable {
    let id: UUID
    let name: String
    let displayName: String
    let description: String
    let targetAudience: String
    let difficultyRange: String
    let colorTheme: String
    let iconName: String
    let learningObjectives: [String]?
    let typicalScenarios: [String]?
    let vocabularyFocus: [String]?
    let personalityTraits: SupabaseAnyCodable?
    let teachingStyle: String?
    let isActive: Bool?
    let createdAt: Date?
    let updatedAt: Date?
    let sortOrder: Int?

    enum CodingKeys: String, CodingKey {
        case id, name, description
        case displayName = "display_name"
        case targetAudience = "target_audience"
        case difficultyRange = "difficulty_range"
        case colorTheme = "color_theme"
        case iconName = "icon_name"
        case learningObjectives = "learning_objectives"
        case typicalScenarios = "typical_scenarios"
        case vocabularyFocus = "vocabulary_focus"
        case personalityTraits = "personality_traits"
        case teachingStyle = "teaching_style"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case sortOrder = "sort_order"
    }
}

struct SupabaseSimulation: Codable, Identifiable {
    let id: UUID
    let personaId: UUID
    let languageId: UUID
    let title: String
    let description: String
    let difficultyLevel: String
    let estimatedDuration: Int
    let scenarioType: String
    let learningObjectives: [String]?
    let vocabularyFocus: [String]?
    let conversationStarters: SupabaseAnyCodable?
    let successCriteria: SupabaseAnyCodable?
    let culturalNotes: String?
    let isActive: Bool?
    let createdAt: Date?
    let updatedAt: Date?

    enum CodingKeys: String, CodingKey {
        case id, title, description
        case personaId = "persona_id"
        case languageId = "language_id"
        case difficultyLevel = "difficulty_level"
        case estimatedDuration = "estimated_duration"
        case scenarioType = "scenario_type"
        case learningObjectives = "learning_objectives"
        case vocabularyFocus = "vocabulary_focus"
        case conversationStarters = "conversation_starters"
        case successCriteria = "success_criteria"
        case culturalNotes = "cultural_notes"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Supabase Client Extensions

extension NIRASupabaseClient {
    func getLessons(language: String) async throws -> [SupabaseLesson] {
        // Convert language name to database code
        let languageCode = getLanguageCode(for: language)
        print("🔍 Converting language '\(language)' to code '\(languageCode)'")

        // First get the language ID
        let languageResponse: [SupabaseLanguageModel] = try await client
            .from("languages")
            .select()
            .eq("code", value: languageCode)
            .execute()
            .value

        guard let languageId = languageResponse.first?.id else {
            print("⚠️ Language '\(language)' (code: '\(languageCode)') not found in database")
            return []
        }

        // Get learning paths for this language
        let pathsResponse: [SupabaseLearningPath] = try await client
            .from("learning_paths")
            .select()
            .eq("language_id", value: languageId.uuidString)
            .eq("is_active", value: true)
            .execute()
            .value

        let pathIds = pathsResponse.map { $0.id.uuidString }

        guard !pathIds.isEmpty else {
            print("⚠️ No learning paths found for language '\(language)'")
            return []
        }

        // Get lessons for these paths with audio fields
        let response: [SupabaseLesson] = try await client
            .from("lessons")
            .select("*, audio_url, has_audio, audio_metadata")
            .in("path_id", values: pathIds)
            .eq("is_active", value: true)
            .order("created_at", ascending: false)
            .execute()
            .value

        print("✅ Loaded \(response.count) lessons for \(language) (\(languageCode))")
        return response
    }

    private func getLanguageCode(for language: String) -> String {
        // Convert from app language names to database language codes
        switch language.lowercased() {
        case "english": return "en"
        case "spanish": return "es"
        case "french": return "fr"
        case "german": return "de"
        case "italian": return "it"
        case "portuguese": return "pt"
        case "japanese": return "ja"
        case "korean": return "ko"
        case "chinese": return "zh"
        case "arabic": return "ar"
        case "hindi": return "hi"
        case "tamil": return "ta"
        case "telugu": return "te"
        case "vietnamese": return "vi"
        case "indonesian": return "id"
        default: return language.lowercased() // fallback to original
        }
    }

    func getLesson(id: UUID) async throws -> SupabaseLesson? {
        let response: [SupabaseLesson] = try await client
            .from("lessons")
            .select("*")
            .eq("id", value: id)
            .execute()
            .value

        return response.first
    }

    func createLesson(_ lesson: SupabaseLesson) async throws {
        _ = try await client
            .from("lessons")
            .insert(lesson)
            .execute()
    }

    func updateLesson(_ lesson: SupabaseLesson) async throws {
        _ = try await client
            .from("lessons")
            .update(lesson)
            .eq("id", value: lesson.id)
            .execute()
    }

    func deleteLesson(id: UUID) async throws {
        _ = try await client
            .from("lessons")
            .delete()
            .eq("id", value: id)
            .execute()
    }
}

// MARK: - Content Upload Helper Types

struct ContentMetadata: Codable {
    let estimatedDuration: Int
    let skillFocus: [String]
    let culturalNotes: String
    let scenario: String
    let setting: String
    let doAndDonts: [String]
}

struct SupabaseLessonContentData: Codable {
    let introduction: String
    let vocabulary: [SupabaseVocabularyItem]
    let dialogues: [SupabaseDialogueItem]
    let exercises: [SupabaseExerciseItem]
    let grammarPoints: [SupabaseGrammarPoint]
}

struct SupabaseLessonMetadata: Codable {
    let estimatedDuration: Int
    let skillFocus: [String]
    let culturalNotes: String
}

struct SupabaseDialogueItem: Codable, Identifiable {
    let id: UUID
    let speaker: String
    let text: String
    let translation: String
    let culturalNote: String?

    init(speaker: String, text: String, translation: String, culturalNote: String?) {
        self.id = UUID()
        self.speaker = speaker
        self.text = text
        self.translation = translation
        self.culturalNote = culturalNote
    }
}

struct SupabaseGrammarPoint: Codable, Identifiable {
    let id: UUID
    let rule: String
    let explanation: String
    let examples: [String]
    let tips: String

    init(rule: String, explanation: String, examples: [String], tips: String) {
        self.id = UUID()
        self.rule = rule
        self.explanation = explanation
        self.examples = examples
        self.tips = tips
    }
}

struct SupabaseExerciseItem: Codable, Identifiable {
    let id: UUID
    let type: String
    let question: String
    let options: [String]?
    let correctAnswer: SupabaseAnyCodable
    let explanation: String
    let hints: [String]?
    let points: Int
    let pairs: [ExercisePair]?

    init(type: String, question: String, options: [String]?, correctAnswer: Int, explanation: String, hints: [String]?, points: Int, pairs: [SupabaseExercisePair]?) {
        self.id = UUID()
        self.type = type
        self.question = question
        self.options = options
        self.correctAnswer = SupabaseAnyCodable(correctAnswer)
        self.explanation = explanation
        self.hints = hints
        self.points = points
        self.pairs = pairs?.map { ExercisePair(left: $0.left, right: $0.right) }
    }
}

struct ExercisePair: Codable {
    let left: String
    let right: String
}

struct SupabaseExercisePair: Codable {
    let left: String
    let right: String
}

// MARK: - Helper Types
// Note: SupabaseError and SupabaseAnyCodable are defined in SupabaseClient.swift to avoid conflicts

