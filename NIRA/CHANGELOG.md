# NIRA Language Learning App - Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.0.0] - 2024-12-19

### Added - Major Language Expansion (26 → 50 Languages)

#### New Languages Added (24 total)
- **European Languages (13)**: Polish, Czech, Hungarian, Romanian, Bulgarian, Croatian, Serbian, Slovak, Slovenian, Estonian, Latvian, Lithuanian, Maltese
- **Celtic Languages (6)**: Irish, Welsh, Scots, Manx, Cornish, Breton  
- **Regional Languages (3)**: Basque, Catalan, Galician
- **Additional Languages (2)**: Urdu

#### 3-Tier Feature Support System
- **Tier 1 (21 languages)**: Full features including voice/live chat
  - English, Spanish, French, German, Italian, Portuguese, Chinese, Japanese, Korean, Hindi, Tamil, Telugu, Arabic, Russian, Dutch, Swedish, Thai, Norwegian, Vietnamese, Indonesian, Bengali
- **Tier 2 (18 languages)**: Partial features, no voice support
  - Kannada, Malayalam, Marathi, Punjabi, Gujarati, Odia, Assamese, Konkani, Sindhi, Bhojpuri, Maithili, Swahili, Hebrew, Greek, Turkish, Farsi, Tagalog, Ukrainian
- **Tier 3 (11 languages)**: Basic text-only features
  - Danish, Xhosa, Zulu, Amharic, Quechua, Maori, Cherokee, Navajo, Hawaiian, Inuktitut, Yoruba, plus all 24 newly added languages

### Changed

#### Core Language Infrastructure
- **Models/User.swift**: Extended `Language` enum with 24 new language cases
- **Services/UserPreferencesService.swift**: Added language preference mappings for all new languages
- **Services/GeminiLiveVoiceService.swift**: Added voice model and locale mappings for new languages
- **Services/PronunciationAssessmentService.swift**: Added locale mappings and practice phrases with default fallbacks
- **Services/RealtimeCollaborationService.swift**: Extended agent language mappings
- **Services/PineconeService.swift**: Added vector space handling for new languages
- **ContentView.swift**: Extended color theme mappings with default fallback
- **Views/Simulation/SimulationBrowserView.swift**: Added UUID mappings for simulation language support

#### Component Improvements
- **Views/Components/FilterChip.swift**: Enhanced to support subtitle and icon properties for better UX
- **Views/AgentsView.swift**: Removed duplicate FilterChip declaration to resolve compilation conflicts

### Fixed
- Resolved FilterChip struct redeclaration compilation error
- Fixed exhaustive switch statement warnings across all service files
- Ensured all language-dependent switch statements handle new languages appropriately
- Added proper default cases for robust error handling

### Technical Details
- All switch statements now include comprehensive coverage or appropriate default cases
- Voice service configurations properly assigned based on language tier
- Locale mappings established for pronunciation assessment features
- UUID mappings created for simulation and content services
- Maintained backward compatibility with existing language implementations

### Build Status
- ✅ Full compilation success with all 50 languages integrated
- ✅ All service files updated and tested
- ✅ No breaking changes to existing functionality

### Migration Notes
- Existing users will automatically have access to new languages
- No database migrations required for language expansion
- All existing user preferences and progress data preserved

---

## [1.0.0] - Previous Release
- Initial release with 26 languages
- Core language learning features
- Agent-based conversation system
- Simulation-based learning
- Voice integration for supported languages
