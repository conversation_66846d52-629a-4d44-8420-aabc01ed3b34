//
//  AgentsView.swift
//  NIRA
//
//  Created by NIRA Team on 2025-01-29.
//

import SwiftUI

struct AgentsView: View {
    @StateObject private var userPreferences = UserPreferencesService.shared
    @State private var selectedPersona: AgentPersona? = nil
    @State private var searchText = ""
    @State private var showingAgentDetail = false
    @State private var selectedAgent: LearningAgent?
    @State private var animateCards = false

    private var filteredAgents: [LearningAgent] {
        var agents = LearningAgent.getAgents(for: userPreferences.selectedLanguage)

        if let selectedPersona = selectedPersona {
            agents = agents.filter { $0.persona == selectedPersona }
        }

        if !searchText.isEmpty {
            agents = agents.filter {
                $0.name.localizedCaseInsensitiveContains(searchText) ||
                $0.persona.rawValue.localizedCaseInsensitiveContains(searchText)
            }
        }

        return agents
    }

    var body: some View {
        VStack(spacing: 0) {
                // Header Section
                headerSection

                // Filters Section
                filtersSection

                // Main Content
                mainContentSection
            }
            .background(
                LinearGradient(
                    colors: [Color.niraThemeIndigo.opacity(0.05), Color.clear],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
        .sheet(isPresented: $showingAgentDetail) {
            if let agent = selectedAgent {
                AgentDetailView(agent: agent)
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 0.6).delay(0.2)) {
                animateCards = true
            }
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            // Title and Language Info
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Learning Companions")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    HStack(spacing: 8) {
                        Text(userPreferences.selectedLanguage.displayName)
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.niraThemeIndigo)

                        Spacer()

                        Text("\(userPreferences.selectedLanguage.learnerCount) learners")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color(.systemGray6))
                            .cornerRadius(8)
                    }
                }

                Spacer()
            }

            // Search Bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                    .accessibilityHidden(true)

                TextField("Search agents...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                    .accessibilityLabel("Search learning agents")
                    .accessibilityHint("Enter text to search for specific learning companions")

                if !searchText.isEmpty {
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                            .frame(minWidth: 44, minHeight: 44)
                    }
                    .accessibilityLabel("Clear search")
                    .accessibilityHint("Removes current search text")
                }
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        }
        .padding()
        .background(Color(.systemBackground))
    }

    // MARK: - Filters Section
    private var filtersSection: some View {
        // Persona Filters
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                FilterChip(
                    title: "All Companions",
                    isSelected: selectedPersona == nil,
                    color: .gray
                ) {
                    selectedPersona = nil
                }

                ForEach(AgentPersona.allCases, id: \.self) { persona in
                    FilterChip(
                        title: persona.rawValue,
                        icon: persona.icon,
                        isSelected: selectedPersona == persona,
                        color: persona.color
                    ) {
                        selectedPersona = selectedPersona == persona ? nil : persona
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical)
        .background(Color(.systemGray6).opacity(0.3))
    }

    // MARK: - Main Content Section
    private var mainContentSection: some View {
        ScrollView {
            if filteredAgents.isEmpty {
                emptyStateView
            } else {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 1), spacing: 16) {
                    ForEach(Array(filteredAgents.enumerated()), id: \.element.id) { index, agent in
                        ModernAgentCard(agent: agent) {
                            selectedAgent = agent
                            showingAgentDetail = true
                        }
                        .scaleEffect(animateCards ? 1.0 : 0.8)
                        .opacity(animateCards ? 1.0 : 0.0)
                        .animation(
                            .easeInOut(duration: 0.4)
                            .delay(Double(index) * 0.1),
                            value: animateCards
                        )
                    }
                }
                .padding()
            }
        }
    }

    // MARK: - Empty State
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "person.3.sequence.fill")
                .font(.system(size: 60))
                .foregroundColor(.niraThemeIndigo.opacity(0.6))

            Text("No Agents Found")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            Text("Try adjusting your filters or search terms")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            Button("Clear Filters") {
                selectedPersona = nil
                searchText = ""
            }
            .font(.headline)
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(Color.niraThemeIndigo.gradient)
            .cornerRadius(12)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Supporting Views



#Preview {
    AgentsView()
}
