import SwiftUI

struct ModernMoreHeader: View {
    let userName: String
    let currentStreak: Int
    @Environment(\.colorScheme) var colorScheme
    @State private var showingThemeSelector = false

    private var currentTimeGreeting: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12: return "Good morning"
        case 12..<17: return "Good afternoon"
        case 17..<22: return "Good evening"
        default: return "Good night"
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            // Status bar background
            Rectangle()
                .fill(Color.white)
                .frame(height: 0)
                .ignoresSafeArea(edges: .top)
            
            // Main header content
            VStack(spacing: 16) {
                // Top row with greeting and theme toggle
                HStack(alignment: .center) {
                    // Greeting and streak section
                    VStack(alignment: .leading, spacing: 4) {
                        Text("\(currentTimeGreeting), \(userName)!")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .lineLimit(1)
                        
                        HStack(spacing: 8) {
                            Image(systemName: "flame.fill")
                                .font(.caption)
                                .foregroundColor(.orange)
                            
                            Text("\(currentStreak) day streak")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.orange)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)

                    // Theme Toggle Button
                    Button(action: {
                        showingThemeSelector = true
                    }) {
                        HStack(spacing: 6) {
                            Image(systemName: colorScheme == .dark ? "moon.fill" : "sun.max.fill")
                                .font(.subheadline)
                                .foregroundColor(colorScheme == .dark ? .blue : .orange)

                            Text(colorScheme == .dark ? "Dark" : "Light")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)
                                .lineLimit(1)

                            Image(systemName: "chevron.down")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color.gray.opacity(0.1))
                        .clipShape(Capsule())
                    }
                    .frame(maxWidth: 120)
                }
                .padding(.horizontal, 20)
            }
            .padding(.top, 12)
            .padding(.bottom, 16)
            .background(Color.white)
        }
        .sheet(isPresented: $showingThemeSelector) {
            ThemeSelectorSheet()
        }
    }
}

// Theme Selector Sheet
struct ThemeSelectorSheet: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) var colorScheme
    @AppStorage("selectedTheme") private var selectedTheme: String = "system"

    private let themes = [
        ("system", "System", "iphone"),
        ("light", "Light", "sun.max.fill"),
        ("dark", "Dark", "moon.fill")
    ]

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Choose your preferred theme")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(.top)

                VStack(spacing: 16) {
                    ForEach(themes, id: \.0) { theme in
                        ThemeOptionRow(
                            id: theme.0,
                            title: theme.1,
                            icon: theme.2,
                            isSelected: selectedTheme == theme.0
                        ) {
                            selectedTheme = theme.0
                            applyTheme(theme.0)
                        }
                    }
                }
                .padding(.horizontal)

                Spacer()
            }
            .navigationTitle("Appearance")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }

    private func applyTheme(_ theme: String) {
        // This would typically be handled by the app's theme manager
        // For now, we'll just store the preference
        selectedTheme = theme
        
        // In a real implementation, you would:
        // 1. Update the app's color scheme
        // 2. Notify other views of the change
        // 3. Persist the setting
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            dismiss()
        }
    }
}

struct ThemeOptionRow: View {
    let id: String
    let title: String
    let icon: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(isSelected ? .blue : .secondary)
                    .frame(width: 30)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text(getThemeDescription(id))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title3)
                        .foregroundColor(.blue)
                } else {
                    Image(systemName: "circle")
                        .font(.title3)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.blue.opacity(0.1) : Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.blue.opacity(0.3) : Color(.systemGray5), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func getThemeDescription(_ themeId: String) -> String {
        switch themeId {
        case "system": return "Follows your device settings"
        case "light": return "Always use light mode"
        case "dark": return "Always use dark mode"
        default: return ""
        }
    }
}

#Preview {
    ModernMoreHeader(
        userName: "Alex",
        currentStreak: 7
    )
    .background(Color.gray.opacity(0.1))
}
