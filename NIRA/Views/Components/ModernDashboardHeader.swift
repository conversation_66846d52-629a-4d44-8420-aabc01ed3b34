import SwiftUI

struct ModernDashboardHeader: View {
    let selectedLanguage: String
    let userName: String
    let currentStreak: Int
    @State private var showingLanguageSelector = false
    @Environment(\.colorScheme) var colorScheme

    private var languageDisplayName: String {
        getLanguageDisplayName(selectedLanguage)
    }

    private var languageFlag: String {
        switch selectedLanguage {
        case "Tamil": return "🇮🇳"
        case "English": return "🇺🇸"
        case "Spanish": return "🇪🇸"
        case "French": return "🇫🇷"
        case "German": return "🇩🇪"
        case "Italian": return "🇮🇹"
        case "Portuguese": return "🇵🇹"
        case "Russian": return "🇷🇺"
        case "Chinese": return "🇨🇳"
        case "Japanese": return "🇯🇵"
        case "Korean": return "🇰🇷"
        case "Arabic": return "🇸🇦"
        case "Hindi": return "🇮🇳"
        case "Bengali": return "🇧🇩"
        case "Telugu": return "🇮🇳"
        case "Marathi": return "🇮🇳"
        case "Gujarati": return "🇮🇳"
        case "Kannada": return "🇮🇳"
        case "Malayalam": return "🇮🇳"
        case "Punjabi": return "🇮🇳"
        case "Urdu": return "🇵🇰"
        default: return "🌍"
        }
    }

    private var currentTimeGreeting: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12: return "Good morning"
        case 12..<17: return "Good afternoon"
        case 17..<22: return "Good evening"
        default: return "Good night"
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            // Status bar background
            Rectangle()
                .fill(colorScheme == .dark ? Color.black : Color.white)
                .frame(height: 0)
                .ignoresSafeArea(edges: .top)

            // Main header content
            VStack(spacing: 16) {
                // Top row with greeting and actions
                HStack(alignment: .center) {
                    // Greeting and streak section
                    VStack(alignment: .leading, spacing: 4) {
                        Text("\(currentTimeGreeting), \(userName)!")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .lineLimit(1)

                        HStack(spacing: 8) {
                            Image(systemName: "flame.fill")
                                .font(.caption)
                                .foregroundColor(.orange)

                            Text("\(currentStreak) day streak")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.orange)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)

                    // Language Selector
                    Button(action: {
                        showingLanguageSelector = true
                    }) {
                        HStack(spacing: 6) {
                            Text(languageFlag)
                                .font(.subheadline)

                            Text(languageDisplayName)
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)
                                .lineLimit(1)

                            Image(systemName: "chevron.down")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color.gray.opacity(0.1))
                        .clipShape(Capsule())
                    }
                    .frame(maxWidth: 120)
                }
                .padding(.horizontal, 20)
            }
            .padding(.top, 12)
            .padding(.bottom, 16)
            .background(colorScheme == .dark ? Color.black : Color.white)
        }
        .sheet(isPresented: $showingLanguageSelector) {
            LanguageSelectorSheet()
        }
    }

    private func getLanguageDisplayName(_ languageCode: String) -> String {
        switch languageCode {
        case "Tamil": return "Tamil"
        case "English": return "English"
        case "Spanish": return "Spanish"
        case "French": return "French"
        case "German": return "German"
        case "Italian": return "Italian"
        case "Portuguese": return "Portuguese"
        case "Russian": return "Russian"
        case "Chinese": return "Chinese"
        case "Japanese": return "Japanese"
        case "Korean": return "Korean"
        case "Arabic": return "Arabic"
        case "Hindi": return "Hindi"
        case "Bengali": return "Bengali"
        case "Telugu": return "Telugu"
        case "Marathi": return "Marathi"
        case "Gujarati": return "Gujarati"
        case "Kannada": return "Kannada"
        case "Malayalam": return "Malayalam"
        case "Punjabi": return "Punjabi"
        case "Urdu": return "Urdu"
        default: return languageCode
        }
    }
}

struct LanguageSelectorSheet: View {
    @Environment(\.dismiss) private var dismiss

    private let languages = [
        ("🇺🇸", "English"), ("🇪🇸", "Spanish"), ("🇫🇷", "French"), ("🇩🇪", "German"),
        ("🇮🇹", "Italian"), ("🇵🇹", "Portuguese"), ("🇨🇳", "Chinese"), ("🇯🇵", "Japanese"),
        ("🇰🇷", "Korean"), ("🇷🇺", "Russian"), ("🇮🇳", "Tamil"), ("🇸🇦", "Arabic"), ("🇮🇳", "Hindi"),
        ("🇮🇳", "Telugu"), ("🇮🇳", "Kannada"), ("🇮🇳", "Malayalam"),
        ("🇧🇩", "Bengali"), ("🇮🇳", "Marathi"), ("🇮🇳", "Punjabi"), ("🇮🇳", "Gujarati"),
        ("🇳🇱", "Dutch"), ("🇸🇪", "Swedish"), ("🇳🇴", "Norwegian"), ("🇹🇭", "Thai"),
        ("🇻🇳", "Vietnamese"), ("🇮🇩", "Indonesian"), ("🇹🇷", "Turkish"), ("🇬🇷", "Greek"),
        ("🇺🇦", "Ukrainian"), ("🇮🇱", "Hebrew"), ("🇮🇷", "Farsi"), ("🇵🇭", "Tagalog"),
        ("🇰🇪", "Swahili"), ("🇿🇦", "Xhosa"), ("🇿🇦", "Zulu"), ("🇳🇬", "Yoruba"),
        ("🇪🇹", "Amharic"), ("🇮🇳", "Odia"), ("🇮🇳", "Assamese"), ("🇮🇳", "Konkani"),
        ("🇵🇰", "Sindhi"), ("🇮🇳", "Bhojpuri"), ("🇮🇳", "Maithili"), ("🇵🇪", "Quechua"),
        ("🇳🇿", "Maori"), ("🇺🇸", "Cherokee"), ("🇺🇸", "Navajo"), ("🇺🇸", "Hawaiian"),
        ("🇨🇦", "Inuktitut"), ("🇩🇰", "Danish"), ("🇵🇱", "Polish")
    ]

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Header
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Choose Your Language")
                            .font(.title2)
                            .fontWeight(.bold)

                        Text("50 languages • AI-powered learning")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal)

                    // Popular Languages
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Popular")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .padding(.horizontal)

                        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                            ForEach(Array(languages.prefix(10).enumerated()), id: \.offset) { index, language in
                                SimpleLanguageCard(flag: language.0, name: language.1, onSelect: dismiss)
                            }
                        }
                        .padding(.horizontal)
                    }

                    // All Languages
                    VStack(alignment: .leading, spacing: 12) {
                        Text("All Languages")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .padding(.horizontal)

                        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                            ForEach(Array(languages.enumerated()), id: \.offset) { index, language in
                                SimpleLanguageCard(flag: language.0, name: language.1, onSelect: dismiss)
                            }
                        }
                        .padding(.horizontal)
                    }
                }
                .padding(.vertical)
            }
            .navigationTitle("Select Language")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .presentationDetents([.large])
        .presentationDragIndicator(.visible)
    }
}

struct SimpleLanguageCard: View {
    let flag: String
    let name: String
    let onSelect: () -> Void

    var body: some View {
        Button(action: onSelect) {
            VStack(spacing: 8) {
                Text(flag)
                    .font(.largeTitle)

                Text(name)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                    .lineLimit(1)
            }
            .frame(height: 80)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.1))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    ModernDashboardHeader(
        selectedLanguage: "Tamil",
        userName: "Alex",
        currentStreak: 7
    )
    .background(Color.gray.opacity(0.1))
}
