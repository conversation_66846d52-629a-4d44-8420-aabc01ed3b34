import SwiftUI

struct ModernDashboardHeader: View {
    let selectedLanguage: String
    let userName: String
    let currentStreak: Int
    @State private var showingLanguageSelector = false
    @State private var showingNotifications = false
    @State private var showingProfile = false

    private var languageDisplayName: String {
        getLanguageDisplayName(selectedLanguage)
    }

    private var languageFlag: String {
        switch selectedLanguage {
        case "Tamil": return "🇮🇳"
        case "English": return "🇺🇸"
        case "Spanish": return "🇪🇸"
        case "French": return "🇫🇷"
        case "German": return "🇩🇪"
        case "Italian": return "🇮🇹"
        case "Portuguese": return "🇵🇹"
        case "Russian": return "🇷🇺"
        case "Chinese": return "🇨🇳"
        case "Japanese": return "🇯🇵"
        case "Korean": return "🇰🇷"
        case "Arabic": return "🇸🇦"
        case "Hindi": return "🇮🇳"
        case "Bengali": return "🇧🇩"
        case "Telugu": return "🇮🇳"
        case "Marathi": return "🇮🇳"
        case "Gujarati": return "🇮🇳"
        case "Kannada": return "🇮🇳"
        case "Malayalam": return "🇮🇳"
        case "Punjabi": return "🇮🇳"
        case "Urdu": return "🇵🇰"
        default: return "🌍"
        }
    }

    private var currentTimeGreeting: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12: return "Good morning"
        case 12..<17: return "Good afternoon"
        case 17..<22: return "Good evening"
        default: return "Good night"
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            // Status bar background
            Rectangle()
                .fill(Color.white)
                .frame(height: 0)
                .ignoresSafeArea(edges: .top)
            
            // Main header content
            VStack(spacing: 16) {
                // Top row with greeting and actions
                HStack(alignment: .center) {
                    // Greeting and streak section
                    VStack(alignment: .leading, spacing: 4) {
                        Text("\(currentTimeGreeting), \(userName)!")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .lineLimit(1)
                        
                        HStack(spacing: 8) {
                            Image(systemName: "flame.fill")
                                .font(.caption)
                                .foregroundColor(.orange)
                            
                            Text("\(currentStreak) day streak")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.orange)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)

                    // Action buttons
                    HStack(spacing: 12) {
                        // Language Selector
                        Button(action: {
                            showingLanguageSelector = true
                        }) {
                            HStack(spacing: 6) {
                                Text(languageFlag)
                                    .font(.subheadline)

                                Text(languageDisplayName)
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.primary)
                                    .lineLimit(1)

                                Image(systemName: "chevron.down")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color.gray.opacity(0.1))
                            .clipShape(Capsule())
                        }
                        .frame(maxWidth: 120)
                        
                        // Notifications
                        Button(action: {
                            showingNotifications = true
                        }) {
                            Image(systemName: "bell")
                                .font(.title3)
                                .foregroundColor(.primary)
                                .frame(width: 44, height: 44)
                        }
                        
                        // Profile
                        Button(action: {
                            showingProfile = true
                        }) {
                            Image(systemName: "person.circle")
                                .font(.title3)
                                .foregroundColor(.primary)
                                .frame(width: 44, height: 44)
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
            .padding(.top, 12)
            .padding(.bottom, 16)
            .background(Color.white)
        }
        .sheet(isPresented: $showingLanguageSelector) {
            LanguageSelectorSheet()
        }
        .sheet(isPresented: $showingNotifications) {
            NotificationsSheet()
        }
        .sheet(isPresented: $showingProfile) {
            ProfileSheet()
        }
    }

    private func getLanguageDisplayName(_ languageCode: String) -> String {
        switch languageCode {
        case "Tamil": return "Tamil"
        case "English": return "English"
        case "Spanish": return "Spanish"
        case "French": return "French"
        case "German": return "German"
        case "Italian": return "Italian"
        case "Portuguese": return "Portuguese"
        case "Russian": return "Russian"
        case "Chinese": return "Chinese"
        case "Japanese": return "Japanese"
        case "Korean": return "Korean"
        case "Arabic": return "Arabic"
        case "Hindi": return "Hindi"
        case "Bengali": return "Bengali"
        case "Telugu": return "Telugu"
        case "Marathi": return "Marathi"
        case "Gujarati": return "Gujarati"
        case "Kannada": return "Kannada"
        case "Malayalam": return "Malayalam"
        case "Punjabi": return "Punjabi"
        case "Urdu": return "Urdu"
        default: return languageCode
        }
    }
}

// Placeholder sheets for notifications and profile
struct NotificationsSheet: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Notifications coming soon!")
                    .font(.title2)
                    .padding()

                Spacer()
            }
            .navigationTitle("Notifications")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct ProfileSheet: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Profile settings coming soon!")
                    .font(.title2)
                    .padding()

                Spacer()
            }
            .navigationTitle("Profile")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    ModernDashboardHeader(
        selectedLanguage: "Tamil",
        userName: "Alex",
        currentStreak: 7
    )
    .background(Color.gray.opacity(0.1))
}
