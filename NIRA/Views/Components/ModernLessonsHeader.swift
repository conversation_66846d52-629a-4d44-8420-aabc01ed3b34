import SwiftUI

struct ModernLessonsHeader: View {
    let selectedLanguage: String
    @Binding var searchText: String
    @State private var showingLanguageSelector = false
    @Environment(\.colorScheme) var colorScheme

    private var languageDisplayName: String {
        getLanguageDisplayName(selectedLanguage)
    }

    private var languageFlag: String {
        switch selectedLanguage {
        case "English": return "🇺🇸"
        case "Spanish": return "🇪🇸"
        case "French": return "🇫🇷"
        case "German": return "🇩🇪"
        case "Italian": return "🇮🇹"
        case "Portuguese": return "🇵🇹"
        case "Chinese": return "🇨🇳"
        case "Japanese": return "🇯🇵"
        case "Korean": return "🇰🇷"
        case "Russian": return "🇷🇺"
        case "Arabic": return "��🇦"
        case "Hindi": return "🇮🇳"
        case "Tamil": return "🇮🇳"
        default: return "🌍"
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            Rectangle()
                .fill(colorScheme == .dark ? Color.black : Color.white)
                .frame(height: 0)
                .ignoresSafeArea(edges: .top)

            VStack(spacing: 12) {
                HStack(alignment: .center) {
                    HStack(spacing: 8) {
                        Image(systemName: "book.fill")
                            .font(.title3)
                            .foregroundColor(.primary)

                        Text("Lessons")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .lineLimit(1)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)

                    Button(action: {
                        showingLanguageSelector = true
                    }) {
                        HStack(spacing: 6) {
                            Text(languageFlag)
                                .font(.subheadline)

                            Text(languageDisplayName)
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)
                                .lineLimit(1)

                            Image(systemName: "chevron.down")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color.gray.opacity(0.1))
                        .clipShape(Capsule())
                    }
                    .frame(maxWidth: 120)
                }
                .padding(.horizontal, 20)

                HStack(spacing: 12) {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.gray)
                        .font(.system(size: 16, weight: .medium))

                    TextField("Search lessons...", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                        .font(.subheadline)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color.gray.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .padding(.horizontal, 20)
            }
            .padding(.top, 12)
            .padding(.bottom, 16)
            .background(colorScheme == .dark ? Color.black : Color.white)
        }
        .sheet(isPresented: $showingLanguageSelector) {
            // Language selector sheet - using shared component
            LanguageSelectorSheet()
        }
    }

    private func getLanguageDisplayName(_ languageCode: String) -> String {
        switch languageCode {
        case "English": return "English"
        case "Spanish": return "Spanish"
        case "French": return "French"
        case "German": return "German"
        case "Italian": return "Italian"
        case "Portuguese": return "Portuguese"
        case "Chinese": return "Chinese"
        case "Japanese": return "Japanese"
        case "Korean": return "Korean"
        case "Russian": return "Russian"
        case "Arabic": return "Arabic"
        case "Hindi": return "Hindi"
        case "Tamil": return "Tamil"
        case "Telugu": return "Telugu"
        case "Kannada": return "Kannada"
        case "Malayalam": return "Malayalam"
        case "Bengali": return "Bengali"
        case "Marathi": return "Marathi"
        case "Punjabi": return "Punjabi"
        case "Gujarati": return "Gujarati"
        case "Dutch": return "Dutch"
        case "Swedish": return "Swedish"
        case "Norwegian": return "Norwegian"
        case "Thai": return "Thai"
        case "Vietnamese": return "Vietnamese"
        case "Indonesian": return "Indonesian"
        case "Turkish": return "Turkish"
        case "Greek": return "Greek"
        case "Ukrainian": return "Ukrainian"
        case "Hebrew": return "Hebrew"
        case "Farsi": return "Farsi"
        case "Tagalog": return "Tagalog"
        default: return languageCode
        }
    }
}
