import SwiftUI

struct ModernLessonsHeader: View {
    let selectedLanguage: String
    @Binding var searchText: String
    @State private var showingLanguageSelector = false

    private var languageDisplayName: String {
        getLanguageDisplayName(selectedLanguage)
    }

    private var languageFlag: String {
        switch selectedLanguage {
        case "Tamil": return "🇮🇳"
        case "English": return "🇺🇸"
        case "Spanish": return "🇪🇸"
        case "French": return "🇫🇷"
        case "German": return "🇩🇪"
        case "Italian": return "🇮🇹"
        case "Portuguese": return "🇵🇹"
        case "Russian": return "🇷🇺"
        case "Chinese": return "🇨🇳"
        case "Japanese": return "🇯🇵"
        case "Korean": return "🇰🇷"
        case "Arabic": return "🇸🇦"
        case "Hindi": return "🇮🇳"
        case "Bengali": return "🇧🇩"
        case "Telugu": return "🇮🇳"
        case "Marathi": return "🇮🇳"
        case "Gujarati": return "🇮🇳"
        case "Kannada": return "🇮🇳"
        case "Malayalam": return "🇮🇳"
        case "Punjabi": return "🇮🇳"
        case "Urdu": return "🇵🇰"
        default: return "🌍"
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            // Gradient Background with proper safe area handling
            ZStack {
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.niraThemeBlue.opacity(0.9),
                        Color.niraThemeTeal.opacity(0.8),
                        Color.niraThemeCyan.opacity(0.7)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )

                // Subtle decorative elements
                GeometryReader { geometry in
                    ZStack {
                        // Floating shapes - more subtle
                        Circle()
                            .fill(Color.white.opacity(0.1))
                            .frame(width: 80, height: 80)
                            .offset(x: geometry.size.width * 0.85, y: 10)
                            .blur(radius: 15)

                        Circle()
                            .fill(Color.white.opacity(0.05))
                            .frame(width: 60, height: 60)
                            .offset(x: geometry.size.width * 0.1, y: 20)
                            .blur(radius: 10)
                    }
                }

                // Header Content
                VStack(spacing: 16) {
                    // Top Row - Simplified
                    HStack {
                        // Simple Title
                        HStack(spacing: 12) {
                            ZStack {
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                                    .frame(width: 40, height: 40)

                                Image(systemName: "book.fill")
                                    .font(.title3)
                                    .foregroundColor(.white)
                            }

                            Text("Lessons")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        }

                        Spacer()

                        // Language Selector - Cleaner design
                        Button(action: {
                            showingLanguageSelector = true
                        }) {
                            HStack(spacing: 8) {
                                Text(languageFlag)
                                    .font(.title3)

                                Text(languageDisplayName)
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.white)

                                Image(systemName: "chevron.down")
                                    .font(.caption)
                                    .foregroundColor(.white.opacity(0.8))
                            }
                            .padding(.horizontal, 14)
                            .padding(.vertical, 8)
                            .background(Color.white.opacity(0.15))
                            .clipShape(Capsule())
                            .overlay(
                                Capsule()
                                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
                            )
                        }
                    }

                    // Search Bar - Better design
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.gray)
                            .font(.system(size: 16, weight: .medium))

                        TextField("Search lessons...", text: $searchText)
                            .textFieldStyle(PlainTextFieldStyle())
                            .font(.subheadline)
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(Color.white.opacity(0.95))
                    .clipShape(Capsule())
                    .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                }
                .padding(.horizontal, 20)
                .padding(.top, 8)
                .padding(.bottom, 16)
            }
            .ignoresSafeArea(edges: .top)
        }
        .sheet(isPresented: $showingLanguageSelector) {
            LanguageSelectorSheet()
        }
    }

    private func getLanguageDisplayName(_ languageCode: String) -> String {
        switch languageCode {
        case "Tamil": return "Tamil"
        case "English": return "English"
        case "Spanish": return "Spanish"
        case "French": return "French"
        case "German": return "German"
        case "Italian": return "Italian"
        case "Portuguese": return "Portuguese"
        case "Russian": return "Russian"
        case "Chinese": return "Chinese"
        case "Japanese": return "Japanese"
        case "Korean": return "Korean"
        case "Arabic": return "Arabic"
        case "Hindi": return "Hindi"
        case "Bengali": return "Bengali"
        case "Telugu": return "Telugu"
        case "Marathi": return "Marathi"
        case "Gujarati": return "Gujarati"
        case "Kannada": return "Kannada"
        case "Malayalam": return "Malayalam"
        case "Punjabi": return "Punjabi"
        case "Urdu": return "Urdu"
        default: return languageCode
        }
    }
}

struct LanguageSelectorSheet: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Language selection coming soon!")
                    .font(.title2)
                    .padding()

                Spacer()
            }
            .navigationTitle("Select Language")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    @Previewable @State var searchText = ""

    return ModernLessonsHeader(
        selectedLanguage: "Tamil",
        searchText: $searchText
    )
    .background(Color(.systemGroupedBackground))
}
