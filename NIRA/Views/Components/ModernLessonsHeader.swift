import SwiftUI

struct ModernLessonsHeader: View {
    let selectedLanguage: String
    @Binding var searchText: String
    @State private var showingLanguageSelector = false

    private var languageDisplayName: String {
        getLanguageDisplayName(selectedLanguage)
    }

    private var languageFlag: String {
        switch selectedLanguage {
        case "Tamil": return "🇮🇳"
        case "English": return "🇺🇸"
        case "Spanish": return "🇪🇸"
        case "French": return "🇫🇷"
        case "German": return "🇩🇪"
        case "Italian": return "🇮🇹"
        case "Portuguese": return "🇵🇹"
        case "Russian": return "🇷🇺"
        case "Chinese": return "🇨🇳"
        case "Japanese": return "🇯🇵"
        case "Korean": return "🇰🇷"
        case "Arabic": return "🇸🇦"
        case "Hindi": return "🇮🇳"
        case "Bengali": return "🇧🇩"
        case "Telugu": return "🇮🇳"
        case "Marathi": return "🇮🇳"
        case "Gujarati": return "🇮🇳"
        case "Kannada": return "🇮🇳"
        case "Malayalam": return "🇮🇳"
        case "Punjabi": return "🇮🇳"
        case "Urdu": return "🇵🇰"
        default: return "🌍"
        }
    }

    var body: some View {
        // Compact header design
        ZStack {
            // LinearGradient commented out - removed blue header background
            /*
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.niraThemeBlue.opacity(0.9),
                    Color.niraThemeTeal.opacity(0.8),
                    Color.niraThemeCyan.opacity(0.7)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            */

            VStack(spacing: 12) {
                // Compact top row
                HStack {
                    // Simple Title
                    HStack(spacing: 8) {
                        Image(systemName: "book.fill")
                            .font(.title3)
                            .foregroundColor(.white)

                        Text("Lessons")
                            .font(.title3)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                    }

                    Spacer()

                    // Compact Language Selector
                    Button(action: {
                        showingLanguageSelector = true
                    }) {
                        HStack(spacing: 6) {
                            Text(languageFlag)
                                .font(.subheadline)

                            Text(languageDisplayName)
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.white)

                            Image(systemName: "chevron.down")
                                .font(.caption2)
                                .foregroundColor(.white.opacity(0.8))
                        }
                        .padding(.horizontal, 10)
                        .padding(.vertical, 6)
                        .background(Color.white.opacity(0.15))
                        .clipShape(Capsule())
                    }
                }

                // Compact Search Bar
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.gray)
                        .font(.system(size: 14, weight: .medium))

                    TextField("Search lessons...", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                        .font(.subheadline)
                }
                .padding(.horizontal, 14)
                .padding(.vertical, 10)
                .background(Color.white.opacity(0.95))
                .clipShape(Capsule())
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
            }
            .padding(.horizontal, 20)
            .padding(.top, 8)
            .padding(.bottom, 12)
        }
        .ignoresSafeArea(edges: .top)
        .sheet(isPresented: $showingLanguageSelector) {
            LanguageSelectorSheet()
        }
    }

    private func getLanguageDisplayName(_ languageCode: String) -> String {
        switch languageCode {
        case "Tamil": return "Tamil"
        case "English": return "English"
        case "Spanish": return "Spanish"
        case "French": return "French"
        case "German": return "German"
        case "Italian": return "Italian"
        case "Portuguese": return "Portuguese"
        case "Russian": return "Russian"
        case "Chinese": return "Chinese"
        case "Japanese": return "Japanese"
        case "Korean": return "Korean"
        case "Arabic": return "Arabic"
        case "Hindi": return "Hindi"
        case "Bengali": return "Bengali"
        case "Telugu": return "Telugu"
        case "Marathi": return "Marathi"
        case "Gujarati": return "Gujarati"
        case "Kannada": return "Kannada"
        case "Malayalam": return "Malayalam"
        case "Punjabi": return "Punjabi"
        case "Urdu": return "Urdu"
        default: return languageCode
        }
    }
}

struct LanguageSelectorSheet: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Language selection coming soon!")
                    .font(.title2)
                    .padding()

                Spacer()
            }
            .navigationTitle("Select Language")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    @Previewable @State var searchText = ""

    ModernLessonsHeader(
        selectedLanguage: "Tamil",
        searchText: $searchText
    )
    .background(Color(.systemGroupedBackground))
}
