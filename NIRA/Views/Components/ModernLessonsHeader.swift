import SwiftUI

struct ModernLessonsHeader: View {
    let selectedLanguage: String
    @Binding var searchText: String
    @State private var showingLanguageSelector = false

    private var languageDisplayName: String {
        getLanguageDisplayName(selectedLanguage)
    }

    private var languageFlag: String {
        switch selectedLanguage {
        case "Tamil": return "🇮🇳"
        case "English": return "🇺🇸"
        case "Spanish": return "🇪🇸"
        case "French": return "🇫🇷"
        case "German": return "🇩🇪"
        case "Italian": return "🇮🇹"
        case "Portuguese": return "🇵🇹"
        case "Russian": return "🇷🇺"
        case "Chinese": return "🇨🇳"
        case "Japanese": return "🇯🇵"
        case "Korean": return "🇰🇷"
        case "Arabic": return "🇸🇦"
        case "Hindi": return "🇮🇳"
        case "Bengali": return "🇧🇩"
        case "Telugu": return "🇮🇳"
        case "Marathi": return "🇮🇳"
        case "Gujarati": return "🇮🇳"
        case "Kannada": return "🇮🇳"
        case "Malayalam": return "🇮🇳"
        case "Punjabi": return "🇮🇳"
        case "Urdu": return "🇵🇰"
        default: return "🌍"
        }
    }

    var body: some View {
        ZStack {
            // Gradient Background
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.purple.opacity(0.8),
                    Color.indigo.opacity(0.9),
                    Color.blue.opacity(0.8)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

            // Decorative Elements
            GeometryReader { geometry in
                ZStack {
                    // Floating shapes
                    Circle()
                        .fill(Color.pink.opacity(0.2))
                        .frame(width: 100, height: 100)
                        .offset(x: geometry.size.width * 0.8, y: -20)
                        .blur(radius: 20)

                    Circle()
                        .fill(Color.cyan.opacity(0.2))
                        .frame(width: 80, height: 80)
                        .offset(x: geometry.size.width * 0.1, y: 10)
                        .blur(radius: 15)

                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.white.opacity(0.1))
                        .frame(width: 60, height: 120)
                        .rotationEffect(.degrees(15))
                        .offset(x: geometry.size.width * 0.9, y: -10)
                        .blur(radius: 10)
                }
            }

            // Header Content
            VStack(spacing: 16) {
                // Top Row
                HStack {
                    // Logo and Title
                    HStack(spacing: 12) {
                        ZStack {
                            Circle()
                                .fill(Color.white)
                                .frame(width: 44, height: 44)
                                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)

                            Image(systemName: "book.fill")
                                .font(.title2)
                                .foregroundColor(.indigo)
                        }

                        VStack(alignment: .leading, spacing: 2) {
                            Text("NIRA Lessons")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)

                            HStack(spacing: 4) {
                                Image(systemName: "sparkles")
                                    .font(.caption)
                                Text("Gen Z Ready")
                                    .font(.caption)
                                    .fontWeight(.medium)
                            }
                            .foregroundColor(.white.opacity(0.9))
                        }
                    }

                    Spacer()

                    // Language Selector
                    Button(action: {
                        showingLanguageSelector = true
                    }) {
                        HStack(spacing: 8) {
                            Text(languageFlag)
                                .font(.title3)

                            Text(languageDisplayName)
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.white)

                            Image(systemName: "chevron.down")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.8))
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(Color.white.opacity(0.2))
                        .clipShape(Capsule())
                        .overlay(
                            Capsule()
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                        )
                    }
                }

                // Search Bar
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.gray)
                        .font(.system(size: 16, weight: .medium))

                    TextField("Search lessons...", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                        .font(.subheadline)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color.white.opacity(0.95))
                .clipShape(Capsule())
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)

                // Quick Categories
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(["🔥 Trending", "⭐ Popular", "🆕 New", "📚 My Path", "❤️ Favorites"], id: \.self) { category in
                            Button(action: {
                                // Handle category selection
                            }) {
                                Text(category)
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 6)
                                    .background(Color.white.opacity(0.2))
                                    .clipShape(Capsule())
                                    .overlay(
                                        Capsule()
                                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                    )
                            }
                        }
                    }
                    .padding(.horizontal, 20)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
        .frame(height: 160)
        .sheet(isPresented: $showingLanguageSelector) {
            LanguageSelectorSheet()
        }
    }

    private func getLanguageDisplayName(_ languageCode: String) -> String {
        switch languageCode {
        case "Tamil": return "Tamil"
        case "English": return "English"
        case "Spanish": return "Spanish"
        case "French": return "French"
        case "German": return "German"
        case "Italian": return "Italian"
        case "Portuguese": return "Portuguese"
        case "Russian": return "Russian"
        case "Chinese": return "Chinese"
        case "Japanese": return "Japanese"
        case "Korean": return "Korean"
        case "Arabic": return "Arabic"
        case "Hindi": return "Hindi"
        case "Bengali": return "Bengali"
        case "Telugu": return "Telugu"
        case "Marathi": return "Marathi"
        case "Gujarati": return "Gujarati"
        case "Kannada": return "Kannada"
        case "Malayalam": return "Malayalam"
        case "Punjabi": return "Punjabi"
        case "Urdu": return "Urdu"
        default: return languageCode
        }
    }
}

struct LanguageSelectorSheet: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Language selection coming soon!")
                    .font(.title2)
                    .padding()

                Spacer()
            }
            .navigationTitle("Select Language")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    @Previewable @State var searchText = ""

    return ModernLessonsHeader(
        selectedLanguage: "Tamil",
        searchText: $searchText
    )
    .background(Color(.systemGroupedBackground))
}
