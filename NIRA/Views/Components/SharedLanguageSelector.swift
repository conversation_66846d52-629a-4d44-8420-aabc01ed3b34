import SwiftUI

struct LanguageSelectorSheet: View {
    @Environment(\.dismiss) private var dismiss

    private let languages = [
        ("🇺🇸", "English"), ("🇪🇸", "Spanish"), ("🇫🇷", "French"), ("🇩🇪", "German"),
        ("🇮🇹", "Italian"), ("🇵🇹", "Portuguese"), ("🇨🇳", "Chinese"), ("🇯🇵", "Japanese"),
        ("🇰🇷", "Korean"), ("🇷🇺", "Russian"), ("🇮🇳", "Tamil"), ("🇸🇦", "Arabic"), ("🇮🇳", "Hindi"),
        ("🇮🇳", "Telugu"), ("🇮🇳", "Kannada"), ("🇮🇳", "Malayalam"),
        ("🇧🇩", "Bengali"), ("🇮🇳", "Marathi"), ("🇮🇳", "Punjabi"), ("🇮🇳", "Gujarati"),
        ("🇳🇱", "Dutch"), ("🇸🇪", "Swedish"), ("🇳🇴", "Norwegian"), ("🇹🇭", "Thai"),
        ("🇻🇳", "Vietnamese"), ("🇮🇩", "Indonesian"), ("🇹🇷", "Turkish"), ("🇬🇷", "Greek"),
        ("🇺🇦", "Ukrainian"), ("🇮🇱", "Hebrew"), ("🇮🇷", "Farsi"), ("🇵🇭", "Tagalog"),
        ("🇰🇪", "Swahili"), ("🇿🇦", "Afrikaans"), ("🇫🇮", "Finnish"), ("🇩🇰", "Danish"),
        ("🇭🇺", "Hungarian"), ("🇨🇿", "Czech"), ("🇵🇱", "Polish"), ("🇷🇴", "Romanian"),
        ("🇧🇬", "Bulgarian"), ("🇭🇷", "Croatian"), ("🇸🇰", "Slovak"), ("🇸🇮", "Slovenian"),
        ("🇱🇻", "Latvian"), ("🇱🇹", "Lithuanian"), ("🇪🇪", "Estonian")
    ]

    private let popularLanguages = [
        ("🇺🇸", "English"), ("🇪🇸", "Spanish"), ("🇫🇷", "French"), ("🇩🇪", "German"),
        ("🇮🇹", "Italian"), ("🇵🇹", "Portuguese"), ("🇨🇳", "Chinese"), ("🇯🇵", "Japanese"),
        ("🇰🇷", "Korean"), ("🇷🇺", "Russian"), ("🇮🇳", "Tamil")
    ]

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                HStack {
                    Text("Select Language")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Button("Done") {
                        dismiss()
                    }
                    .font(.headline)
                    .foregroundColor(.niraPrimary)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
                .background(Color(.systemBackground))
                
                ScrollView {
                    LazyVStack(spacing: 20) {
                        // Popular Languages Section
                        VStack(alignment: .leading, spacing: 16) {
                            HStack {
                                Text("Popular Languages")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)
                                Spacer()
                            }
                            .padding(.horizontal, 20)
                            
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                                ForEach(Array(popularLanguages.enumerated()), id: \.offset) { index, language in
                                    SimpleLanguageCard(
                                        flag: language.0,
                                        name: language.1,
                                        isPopular: true
                                    )
                                }
                            }
                            .padding(.horizontal, 20)
                        }
                        
                        // All Languages Section
                        VStack(alignment: .leading, spacing: 16) {
                            HStack {
                                Text("All Languages")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)
                                Spacer()
                            }
                            .padding(.horizontal, 20)
                            
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                                ForEach(Array(languages.enumerated()), id: \.offset) { index, language in
                                    SimpleLanguageCard(
                                        flag: language.0,
                                        name: language.1,
                                        isPopular: false
                                    )
                                }
                            }
                            .padding(.horizontal, 20)
                        }
                    }
                    .padding(.vertical, 20)
                }
            }
            .background(Color(.systemGroupedBackground))
        }
    }
}

struct SimpleLanguageCard: View {
    let flag: String
    let name: String
    let isPopular: Bool
    
    var body: some View {
        Button(action: {
            // Handle language selection
        }) {
            HStack(spacing: 12) {
                Text(flag)
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)
                    
                    if isPopular {
                        Text("Popular")
                            .font(.caption)
                            .foregroundColor(.niraPrimary)
                            .fontWeight(.medium)
                    }
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(16)
            .background(Color(.systemBackground))
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }
}
