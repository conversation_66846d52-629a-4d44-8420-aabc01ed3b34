//
//  ModernSimulationCard.swift
//  NIRA
//
//  Created by NIRA Team on 1/2025.
//

import SwiftUI

struct ModernSimulationCard: View {
    let simulation: SupabaseSimulation
    let onStart: () -> Void
    @State private var isPressed = false
    
    private var difficultyColor: Color {
        switch simulation.difficultyLevel {
        case 1: return .levelA1Color
        case 2: return .levelA2Color
        case 3: return .levelB1Color
        case 4: return .levelB2Color
        case 5: return .levelC1Color
        case 6: return .levelC2Color
        default: return .levelA1Color
        }
    }
    
    private var difficultyGradient: [Color] {
        switch simulation.difficultyLevel {
        case 1: return Color.levelA1Gradient
        case 2: return Color.levelA2Gradient
        case 3: return Color.levelB1Gradient
        case 4: return Color.levelB2Gradient
        case 5: return Color.levelC1Gradient
        case 6: return Color.levelC2Gradient
        default: return Color.levelA1Gradient
        }
    }
    
    private var difficultyText: String {
        switch simulation.difficultyLevel {
        case 1: return "A1"
        case 2: return "A2"
        case 3: return "B1"
        case 4: return "B2"
        case 5: return "C1"
        case 6: return "C2"
        default: return "A1"
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header with cultural context
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(simulation.title)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.primaryText)
                        .lineLimit(2)
                    
                    if let culturalContext = simulation.culturalContext {
                        HStack(spacing: 4) {
                            Image(systemName: "globe")
                                .font(.caption)
                                .foregroundColor(difficultyColor)
                            
                            Text(culturalContext)
                                .font(.caption)
                                .foregroundColor(.secondaryText)
                        }
                    }
                }
                
                Spacer()
                
                // Difficulty badge
                Text(difficultyText)
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        LinearGradient(
                            colors: difficultyGradient,
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(8)
            }
            
            // Conversation preview
            if let conversationPreview = simulation.conversationPreview {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Preview")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondaryText)
                        .textCase(.uppercase)
                    
                    Text(conversationPreview)
                        .font(.subheadline)
                        .foregroundColor(.primaryText)
                        .lineLimit(3)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color.cardBackground.opacity(0.5))
                        .cornerRadius(8)
                }
            }
            
            // Features row
            HStack(spacing: 16) {
                // Voice mode indicator
                HStack(spacing: 4) {
                    Image(systemName: simulation.hasVoiceMode ? "mic.fill" : "text.bubble.fill")
                        .font(.caption)
                        .foregroundColor(simulation.hasVoiceMode ? .niraThemeSuccess : .niraThemeTeal)
                    
                    Text(simulation.hasVoiceMode ? "Voice" : "Text")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.secondaryText)
                }
                
                // AI agent indicator
                if simulation.hasAIAgent {
                    HStack(spacing: 4) {
                        Image(systemName: "brain.head.profile")
                            .font(.caption)
                            .foregroundColor(.niraThemeIndigo)
                        
                        Text("AI Agent")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.secondaryText)
                    }
                }
                
                Spacer()
                
                // Duration
                if let duration = simulation.estimatedDuration {
                    HStack(spacing: 4) {
                        Image(systemName: "clock")
                            .font(.caption)
                            .foregroundColor(.secondaryText)
                        
                        Text("\(duration) min")
                            .font(.caption)
                            .foregroundColor(.secondaryText)
                    }
                }
            }
            
            // Start button
            Button(action: onStart) {
                HStack {
                    Image(systemName: "play.fill")
                        .font(.subheadline)
                    
                    Text("Start Simulation")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(
                    LinearGradient(
                        colors: difficultyGradient,
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(
            color: difficultyColor.opacity(0.2),
            radius: 8,
            x: 0,
            y: 4
        )
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

// MARK: - Preview

#Preview {
    ModernSimulationCard(
        simulation: SupabaseSimulation(
            id: UUID(),
            title: "Ordering Coffee in Paris",
            description: "Practice ordering coffee at a Parisian café",
            difficultyLevel: 2,
            languageCode: "fr",
            languageName: "French",
            scenario: "café",
            culturalContext: "Paris, France",
            conversationPreview: "Bonjour! Je voudrais un café, s'il vous plaît.",
            hasVoiceMode: true,
            hasAIAgent: true,
            estimatedDuration: 5,
            isActive: true,
            createdAt: Date(),
            updatedAt: Date()
        ),
        onStart: {}
    )
    .padding()
}
