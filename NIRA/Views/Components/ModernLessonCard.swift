import SwiftUI

struct ModernLessonCard: View {
    let lesson: SupabaseLesson
    let language: String
    let onStart: () -> Void

    private var levelColor: Color {
        return Color.getLevelColor(for: lesson.difficultyLevel ?? 1)
    }

    private var levelGradient: [Color] {
        return Color.getLevelGradient(for: lesson.difficultyLevel ?? 1)
    }

    private var levelText: String {
        switch lesson.difficultyLevel {
        case 1: return "A1"
        case 2: return "A2"
        case 3: return "B1"
        case 4: return "B2"
        case 5: return "C1"
        case 6: return "C2"
        default: return "A1"
        }
    }

    private var gradientColors: [Color] {
        return levelGradient.map { $0.opacity(0.9) }
    }

    private var iconName: String {
        let title = lesson.title.lowercased()
        if title.contains("conversation") || title.contains("chat") {
            return "bubble.left.and.bubble.right.fill"
        } else if title.contains("grammar") {
            return "textformat.abc"
        } else if title.contains("vocabulary") || title.contains("words") {
            return "book.fill"
        } else if title.contains("culture") || title.contains("holiday") {
            return "globe.americas.fill"
        } else if title.contains("technology") || title.contains("tech") {
            return "laptopcomputer"
        } else if title.contains("business") || title.contains("work") {
            return "briefcase.fill"
        } else if title.contains("travel") {
            return "airplane"
        } else if title.contains("food") || title.contains("restaurant") {
            return "fork.knife"
        } else if title.contains("health") || title.contains("medical") {
            return "heart.fill"
        } else if title.contains("family") || title.contains("home") {
            return "house.fill"
        } else {
            return "graduationcap.fill"
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            // Header with gradient background
            ZStack {
                LinearGradient(
                    gradient: Gradient(colors: gradientColors),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )

                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(language)
                                .font(.headline)
                                .fontWeight(.bold)
                                .foregroundColor(.white)

                            HStack(spacing: 6) {
                                Text(levelText)
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.white.opacity(0.9))
                                    .foregroundColor(levelColor)
                                    .clipShape(Capsule())
                            }
                        }

                        Spacer()

                        HStack(spacing: 4) {
                            Image(systemName: "clock.fill")
                                .font(.caption)
                            Text("25m")
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(.white.opacity(0.9))
                    }

                    HStack {
                        Image(systemName: iconName)
                            .font(.title2)
                            .foregroundColor(.white)

                        Spacer()
                    }
                }
                .padding(16)
            }
            .frame(height: 120)

            // Content section
            VStack(alignment: .leading, spacing: 12) {
                VStack(alignment: .leading, spacing: 6) {
                    Text(lesson.title)
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)

                    Text(lesson.description ?? "No description available")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                }

                // Goals section
                VStack(alignment: .leading, spacing: 8) {
                    HStack(spacing: 6) {
                        Image(systemName: "target")
                            .font(.caption)
                            .foregroundColor(levelColor)
                        Text("Learning Goals")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                    }

                    VStack(alignment: .leading, spacing: 4) {
                        goalRow("Master essential vocabulary")
                        goalRow("Practice pronunciation")
                        goalRow("Understand cultural context")
                    }
                }

                // Start button
                Button(action: onStart) {
                    HStack(spacing: 8) {
                        Image(systemName: "play.fill")
                            .font(.subheadline)
                        Text("Start Lesson")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [levelColor, levelColor.opacity(0.8)]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                }
                .buttonStyle(PlainButtonStyle())
                .scaleEffect(1.0)
                .animation(.easeInOut(duration: 0.1), value: false)
            }
            .padding(16)
        }
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color(.systemGray5), lineWidth: 1)
        )
    }

    private func goalRow(_ text: String) -> some View {
        HStack(spacing: 6) {
            Circle()
                .fill(levelColor.opacity(0.3))
                .frame(width: 4, height: 4)
            Text(text)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
}

#Preview {
    // Create a mock lesson using JSON decoding for preview
    let jsonData = """
    {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "path_id": "123e4567-e89b-12d3-a456-426614174001",
        "title": "Everyday Conversations",
        "description": "Learn essential phrases for common social situations and daily interactions",
        "lesson_type": "conversation",
        "difficulty_level": 3,
        "estimated_duration": 25,
        "sequence_order": 1,
        "learning_objectives": ["Master conversation starters", "Practice greetings"],
        "vocabulary_focus": ["greetings", "introductions"],
        "is_active": true,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "has_audio": false
    }
    """.data(using: .utf8)!

    let mockLesson = try! JSONDecoder().decode(SupabaseLesson.self, from: jsonData)

    ModernLessonCard(
        lesson: mockLesson,
        language: "Tamil",
        onStart: {}
    )
    .padding()
    .background(Color(.systemGroupedBackground))
}
