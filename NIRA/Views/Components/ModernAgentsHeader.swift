import SwiftUI

struct ModernAgentsHeader: View {
    let selectedLanguage: String
    @Binding var searchText: String
    @State private var showingLanguageSelector = false

    private var languageDisplayName: String {
        getLanguageDisplayName(selectedLanguage)
    }

    private var languageFlag: String {
        switch selectedLanguage {
        case "Tamil": return "🇮🇳"
        case "English": return "🇺🇸"
        case "Spanish": return "🇪🇸"
        case "French": return "🇫🇷"
        case "German": return "🇩🇪"
        case "Italian": return "🇮🇹"
        case "Portuguese": return "🇵🇹"
        case "Russian": return "🇷🇺"
        case "Chinese": return "🇨🇳"
        case "Japanese": return "🇯🇵"
        case "Korean": return "🇰🇷"
        case "Arabic": return "🇸🇦"
        case "Hindi": return "🇮🇳"
        case "Bengali": return "🇧🇩"
        case "Telugu": return "🇮🇳"
        case "Marathi": return "🇮🇳"
        case "Gujarati": return "🇮🇳"
        case "Kannada": return "🇮🇳"
        case "Malayalam": return "🇮🇳"
        case "Punjabi": return "🇮🇳"
        case "Urdu": return "🇵🇰"
        default: return "🌍"
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            // Status bar background
            Rectangle()
                .fill(Color.white)
                .frame(height: 0)
                .ignoresSafeArea(edges: .top)
            
            // Main header content
            VStack(spacing: 12) {
                // Top row with title and language selector
                HStack(alignment: .center) {
                    // Title section
                    HStack(spacing: 8) {
                        Image(systemName: "person.2.fill")
                            .font(.title3)
                            .foregroundColor(.primary)

                        Text("Agents")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .lineLimit(1)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)

                    // Language Selector
                    Button(action: {
                        showingLanguageSelector = true
                    }) {
                        HStack(spacing: 6) {
                            Text(languageFlag)
                                .font(.subheadline)

                            Text(languageDisplayName)
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)
                                .lineLimit(1)

                            Image(systemName: "chevron.down")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color.gray.opacity(0.1))
                        .clipShape(Capsule())
                    }
                    .frame(maxWidth: 120)
                }
                .padding(.horizontal, 20)

                // Search Bar
                HStack(spacing: 12) {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.gray)
                        .font(.system(size: 16, weight: .medium))

                    TextField("Search agents...", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                        .font(.subheadline)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color.gray.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .padding(.horizontal, 20)
            }
            .padding(.top, 12)
            .padding(.bottom, 16)
            .background(Color.white)
        }
        .sheet(isPresented: $showingLanguageSelector) {
            LanguageSelectorSheet()
        }
    }

    private func getLanguageDisplayName(_ languageCode: String) -> String {
        switch languageCode {
        case "Tamil": return "Tamil"
        case "English": return "English"
        case "Spanish": return "Spanish"
        case "French": return "French"
        case "German": return "German"
        case "Italian": return "Italian"
        case "Portuguese": return "Portuguese"
        case "Russian": return "Russian"
        case "Chinese": return "Chinese"
        case "Japanese": return "Japanese"
        case "Korean": return "Korean"
        case "Arabic": return "Arabic"
        case "Hindi": return "Hindi"
        case "Bengali": return "Bengali"
        case "Telugu": return "Telugu"
        case "Marathi": return "Marathi"
        case "Gujarati": return "Gujarati"
        case "Kannada": return "Kannada"
        case "Malayalam": return "Malayalam"
        case "Punjabi": return "Punjabi"
        case "Urdu": return "Urdu"
        default: return languageCode
        }
    }
}

#Preview {
    @Previewable @State var searchText = ""

    ModernAgentsHeader(
        selectedLanguage: "Tamil",
        searchText: $searchText
    )
    .background(Color.gray.opacity(0.1))
}
