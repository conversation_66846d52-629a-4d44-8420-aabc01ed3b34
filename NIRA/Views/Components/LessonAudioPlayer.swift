import SwiftUI
import AVFoundation

struct LessonAudioPlayer: View {
    let audioUrl: String
    let audioMetadata: [String: Any]?

    @State private var player: AVPlayer?
    @State private var isPlaying = false
    @State private var currentTime: Double = 0
    @State private var duration: Double = 0
    @State private var isLoading = false
    @State private var hasError = false
    @State private var errorMessage = ""
    @State private var observer: AudioPlayerObserver?

    private let timer = Timer.publish(every: 0.1, on: .main, in: .common).autoconnect()

    var body: some View {
        VStack(spacing: 12) {
            // Audio player header
            HStack {
                Image(systemName: "waveform")
                    .foregroundColor(.niraPrimary)
                    .font(.title2)

                VStack(alignment: .leading, spacing: 2) {
                    Text("Lesson Audio")
                        .font(.headline)
                        .foregroundColor(.primary)

                    if let metadata = audioMetadata,
                       let voiceUsed = metadata["voice_used"] as? String {
                        Text("Voice: \(voiceUsed)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                if isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }

            // Progress bar
            if duration > 0 {
                VStack(spacing: 4) {
                    ProgressView(value: currentTime, total: duration)
                        .progressViewStyle(LinearProgressViewStyle(tint: .niraPrimary))

                    HStack {
                        Text(formatTime(currentTime))
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Spacer()

                        Text(formatTime(duration))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            // Control buttons
            HStack(spacing: 20) {
                // Rewind 10 seconds
                Button(action: rewind) {
                    Image(systemName: "gobackward.10")
                        .font(.title2)
                        .foregroundColor(.niraPrimary)
                }
                .disabled(player == nil)

                // Play/Pause button
                Button(action: togglePlayPause) {
                    Image(systemName: isPlaying ? "pause.circle.fill" : "play.circle.fill")
                        .font(.largeTitle)
                        .foregroundColor(.niraPrimary)
                }
                .disabled(player == nil || hasError)

                // Forward 10 seconds
                Button(action: fastForward) {
                    Image(systemName: "goforward.10")
                        .font(.title2)
                        .foregroundColor(.niraPrimary)
                }
                .disabled(player == nil)
            }

            // Error message
            if hasError {
                Text(errorMessage)
                    .font(.caption)
                    .foregroundColor(.red)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .onAppear {
            setupAudioPlayer()
        }
        .onDisappear {
            cleanupPlayer()
        }
        .onReceive(timer) { _ in
            updateProgress()
        }
    }

    // MARK: - Audio Player Functions

    private func setupAudioPlayer() {
        guard let url = URL(string: audioUrl) else {
            hasError = true
            errorMessage = "Invalid audio URL"
            return
        }

        isLoading = true
        hasError = false

        // Configure audio session
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default)
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("Failed to set audio session category: \(error)")
        }

        // Create player
        player = AVPlayer(url: url)

        // Create and configure observer
        observer = AudioPlayerObserver()
        observer?.onStatusChange = { [weak self] status in
            DispatchQueue.main.async {
                switch status {
                case .readyToPlay:
                    self?.isLoading = false
                    self?.hasError = false
                case .failed:
                    self?.isLoading = false
                    self?.hasError = true
                    self?.errorMessage = "Failed to load audio"
                case .unknown:
                    break
                @unknown default:
                    break
                }
            }
        }

        observer?.onDurationChange = { [weak self] duration in
            DispatchQueue.main.async {
                self?.duration = duration
            }
        }

        // Add observers
        if let observer = observer {
            player?.addObserver(observer, forKeyPath: "status", options: [.new], context: nil)
            player?.currentItem?.addObserver(observer, forKeyPath: "duration", options: [.new], context: nil)
        }

        // Listen for playback end
        NotificationCenter.default.addObserver(
            forName: .AVPlayerItemDidPlayToEndTime,
            object: player?.currentItem,
            queue: .main
        ) { _ in
            isPlaying = false
            currentTime = 0
            player?.seek(to: .zero)
        }

        isLoading = false
    }

    private func togglePlayPause() {
        guard let player = player else { return }

        if isPlaying {
            player.pause()
            isPlaying = false
        } else {
            player.play()
            isPlaying = true
        }
    }

    private func rewind() {
        guard let player = player else { return }
        let newTime = max(0, currentTime - 10)
        player.seek(to: CMTime(seconds: newTime, preferredTimescale: 1))
    }

    private func fastForward() {
        guard let player = player else { return }
        let newTime = min(duration, currentTime + 10)
        player.seek(to: CMTime(seconds: newTime, preferredTimescale: 1))
    }

    private func updateProgress() {
        guard let player = player else { return }

        currentTime = player.currentTime().seconds

        if let duration = player.currentItem?.duration.seconds,
           duration.isFinite {
            self.duration = duration
        }
    }

    private func cleanupPlayer() {
        player?.pause()

        if let observer = observer {
            player?.removeObserver(observer, forKeyPath: "status")
            player?.currentItem?.removeObserver(observer, forKeyPath: "duration")
        }

        NotificationCenter.default.removeObserver(self)
        player = nil
        observer = nil
    }

    private func formatTime(_ time: Double) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

// MARK: - Audio Player Observer

class AudioPlayerObserver: NSObject {
    var onStatusChange: ((AVPlayer.Status) -> Void)?
    var onDurationChange: ((Double) -> Void)?

    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        if keyPath == "status" {
            if let player = object as? AVPlayer {
                onStatusChange?(player.status)
            }
        } else if keyPath == "duration" {
            if let playerItem = object as? AVPlayerItem,
               playerItem.duration.seconds.isFinite {
                onDurationChange?(playerItem.duration.seconds)
            }
        }
    }
}

#Preview {
    LessonAudioPlayer(
        audioUrl: "https://example.com/audio.mp3",
        audioMetadata: [
            "voice_used": "Rachel",
            "language": "en",
            "duration_estimate": 30
        ]
    )
    .padding()
}
