import SwiftUI

// MARK: - Shared Tile Components

struct TileItem {
    let title: String
    let subtitle: String
    let color: Color
}

struct CategoryTileSection: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let items: [TileItem]
    @State private var showingItems = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Category header with illustration
            HStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    Text(title)
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.white)
                    
                    Text(subtitle)
                        .font(.system(size: 16))
                        .foregroundColor(.white.opacity(0.8))
                }
                
                Spacer()
                
                // Colorful illustration circle
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [color, color.opacity(0.7)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)
                    
                    Image(systemName: icon)
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(.white)
                }
            }
            
            // Tiles grid
            if showingItems {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                    ForEach(Array(items.enumerated()), id: \.offset) { index, item in
                        TileView(item: item)
                    }
                }
                .transition(.opacity.combined(with: .scale))
            }
            
            // Hide/Show toggle button
            Button(action: {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    showingItems.toggle()
                }
            }) {
                HStack(spacing: 8) {
                    Image(systemName: showingItems ? "chevron.up" : "chevron.down")
                        .font(.system(size: 14, weight: .medium))
                    Text(showingItems ? "Hide \(title.lowercased())" : "Show \(title.lowercased())")
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.white.opacity(0.8))
            }
        }
        .onAppear {
            // Auto-show first category
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    showingItems = true
                }
            }
        }
    }
}

struct TileView: View {
    let item: TileItem
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(item.title)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
            
            if !item.subtitle.isEmpty {
                Text(item.subtitle)
                    .font(.system(size: 14))
                    .foregroundColor(.white.opacity(0.7))
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [item.color, item.color.opacity(0.8)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        )
    }
}

#Preview {
    VStack(spacing: 24) {
        CategoryTileSection(
            title: "Sample Category",
            subtitle: "Example tile section",
            icon: "book.fill",
            color: .emojiGreen,
            items: [
                TileItem(
                    title: "Sample Tile 1",
                    subtitle: "This is a sample tile",
                    color: .emojiGreen
                ),
                TileItem(
                    title: "Sample Tile 2",
                    subtitle: "Another sample tile",
                    color: .emojiGreen
                )
            ]
        )
    }
    .padding()
    .background(Color.black)
    .preferredColorScheme(.dark)
}
