//
//  SimulationsView.swift
//  NIRA
//
//  Created by NIRA Team on 27/05/2025.
//

import SwiftUI

// MARK: - Simulations View

struct SimulationsView: View {
    @State private var selectedCategory: SimulationCategory = .all
    @State private var selectedLanguage: Language = .french
    @State private var showingSimulation = false
    @State private var selectedSimulation: CulturalSimulation?

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    simulationHeaderSection

                    // Language Filter
                    languageFilterSection

                    // Category Filter
                    categoryFilterSection

                    // Simulations Grid
                    simulationsGridSection

                    // Benefits Section
                    benefitsSection
                }
                .padding()
            }
            .navigationTitle("Cultural Simulations")
            .background(
                LinearGradient(
                    colors: [Color.niraThemeTeal.opacity(0.05), Color.clear],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
        }
        .sheet(isPresented: $showingSimulation) {
            if let simulation = selectedSimulation {
                SimulationDetailView(simulation: simulation)
            }
        }
    }

    // MARK: - Header Section

    private var simulationHeaderSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Cultural Simulations")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.primaryText)

            Text("Practice real-world conversations in authentic cultural contexts")
                .font(.subheadline)
                .foregroundColor(.secondaryText)
                .multilineTextAlignment(.leading)
        }
    }

    // MARK: - Language Filter Section

    private var languageFilterSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Select Language")
                .font(.headline)
                .fontWeight(.semibold)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(Language.allCases, id: \.self) { language in
                        FilterChip(
                            title: language.displayName,
                            isSelected: selectedLanguage == language,
                            color: .niraThemeBlue,
                            action: { selectedLanguage = language }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
    }

    // MARK: - Category Filter Section

    private var categoryFilterSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Categories")
                .font(.headline)
                .fontWeight(.semibold)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(SimulationCategory.allCases, id: \.self) { category in
                        FilterChip(
                            title: category.displayName,
                            isSelected: selectedCategory == category,
                            color: .niraThemeTeal,
                            action: { selectedCategory = category }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
    }

    // MARK: - Simulations Grid Section

    private var simulationsGridSection: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 1), spacing: 16) {
            ForEach(filteredSimulations, id: \.id) { simulation in
                ModernSimulationCard(culturalSimulation: simulation) {
                    selectedSimulation = simulation
                    showingSimulation = true
                }
            }
        }
    }

    // MARK: - Benefits Section

    private var benefitsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Why Cultural Simulations?")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                BenefitRow(icon: "globe.americas.fill", title: "Real-World Context", description: "Learn language in authentic cultural situations")
                BenefitRow(icon: "person.2.fill", title: "Social Skills", description: "Practice conversations and social interactions")
                BenefitRow(icon: "brain.head.profile", title: "Cultural Intelligence", description: "Understand customs, etiquette, and social norms")
                BenefitRow(icon: "star.fill", title: "Confidence Building", description: "Gain confidence for real-world interactions")
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .emojiGreen.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }

    // MARK: - Computed Properties

    private var filteredSimulations: [CulturalSimulation] {
        // Show simulations for all languages, not just the selected one
        let allSimulations = CulturalSimulation.mockSimulations + CulturalSimulation.additionalSimulations

        if selectedCategory == .all {
            return allSimulations
        } else {
            return allSimulations.filter { $0.category == selectedCategory }
        }
    }
}

// MARK: - Supporting Views

struct SimulationCard: View {
    let simulation: CulturalSimulation
    let action: () -> Void
    @State private var isPressed = false

    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 16) {
                // Header with icon and category
                HStack {
                    Text(simulation.imageIcon)
                        .font(.system(size: 40))

                    Spacer()

                    VStack(alignment: .trailing, spacing: 4) {
                        Text(simulation.category.displayName)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(Color.emojiGreen.opacity(0.2))
                            .foregroundColor(.emojiGreen)
                            .cornerRadius(8)

                        HStack(spacing: 4) {
                            Image(systemName: "star.fill")
                                .font(.caption2)
                                .foregroundColor(.yellow)
                            Text(String(format: "%.1f", simulation.rating))
                                .font(.caption2)
                                .fontWeight(.medium)
                        }
                    }
                }

                // Title and description
                VStack(alignment: .leading, spacing: 8) {
                    Text(simulation.title)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text(simulation.description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }

                // Details row
                HStack(spacing: 16) {
                    DetailItem(icon: "clock", text: simulation.duration)
                    DetailItem(icon: "person.2", text: "\(simulation.participants) people")
                    DetailItem(icon: "chart.bar", text: simulation.difficulty)
                }

                // Progress bar
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text("Completion Rate")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Spacer()
                        Text("\(Int(simulation.completionRate * 100))%")
                            .font(.caption)
                            .fontWeight(.medium)
                    }

                    ProgressView(value: simulation.completionRate)
                        .progressViewStyle(LinearProgressViewStyle(tint: .emojiGreen))
                }

                // Start button
                HStack {
                    Image(systemName: "play.fill")
                        .font(.caption)
                    Text("Start Simulation")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(Color.emojiGreen.gradient)
                .cornerRadius(12)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .emojiGreen.opacity(isPressed ? 0.2 : 0.1), radius: isPressed ? 8 : 4, x: 0, y: isPressed ? 4 : 2)
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

struct DetailItem: View {
    let icon: String
    let text: String

    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.emojiGreen)
            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

struct BenefitRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.emojiGreen)
                .frame(width: 24, height: 24)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primaryText)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondaryText)
            }

            Spacer()
        }
    }
}

struct SimulationDetailView: View {
    let simulation: CulturalSimulation
    @Environment(\.dismiss) private var dismiss
    @State private var animateElements = false

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Dark background like in the reference images
                Color.black
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Header with back button and title
                    simulationHeader

                    // Main content with tile-based categories
                    ScrollView {
                        LazyVStack(spacing: 24) {
                            // Scenario Category
                            CategoryTileSection(
                                title: "Scenario Overview",
                                subtitle: "Understand the simulation context",
                                icon: "theatermasks.fill",
                                color: .emojiBlue,
                                items: [
                                    TileItem(
                                        title: simulation.scenario,
                                        subtitle: "\(simulation.duration) • \(simulation.participants) participants",
                                        color: .emojiBlue
                                    )
                                ]
                            )

                            // Learning Objectives Category
                            if !simulation.learningObjectives.isEmpty {
                                CategoryTileSection(
                                    title: "Learning Goals",
                                    subtitle: "What you'll master in this simulation",
                                    icon: "target",
                                    color: .emojiGreen,
                                    items: simulation.learningObjectives.map { objective in
                                        TileItem(
                                            title: objective,
                                            subtitle: "Essential skill",
                                            color: .emojiGreen
                                        )
                                    }
                                )
                            }

                            // Cultural Notes Category
                            if !simulation.culturalNotes.isEmpty {
                                CategoryTileSection(
                                    title: "Cultural Insights",
                                    subtitle: "Important cultural context",
                                    icon: "lightbulb.fill",
                                    color: .emojiOrange,
                                    items: simulation.culturalNotes.map { note in
                                        TileItem(
                                            title: note,
                                            subtitle: "Cultural tip",
                                            color: .emojiOrange
                                        )
                                    }
                                )
                            }

                            // Simulation Details Category
                            CategoryTileSection(
                                title: "Simulation Details",
                                subtitle: "Key information about this experience",
                                icon: "info.circle.fill",
                                color: .emojiPurple,
                                items: [
                                    TileItem(
                                        title: "Difficulty: \(simulation.difficulty)",
                                        subtitle: "Challenge level",
                                        color: .emojiPurple
                                    ),
                                    TileItem(
                                        title: "Category: \(simulation.category.displayName)",
                                        subtitle: "Simulation type",
                                        color: .emojiPurple
                                    ),
                                    TileItem(
                                        title: "Rating: \(String(format: "%.1f", simulation.rating)) ⭐",
                                        subtitle: "User rating",
                                        color: .emojiPurple
                                    ),
                                    TileItem(
                                        title: "Completion: \(Int(simulation.completionRate * 100))%",
                                        subtitle: "Success rate",
                                        color: .emojiPurple
                                    )
                                ]
                            )
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 16)
                    }

                    // Start Button
                    Button(action: {
                        // TODO: Start simulation
                        dismiss()
                    }) {
                        HStack(spacing: 12) {
                            Image(systemName: "play.fill")
                                .font(.system(size: 16, weight: .semibold))

                            Text("Start Simulation")
                                .font(.system(size: 18, weight: .semibold))
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity, minHeight: 56)
                        .background(
                            LinearGradient(
                                colors: [.emojiGreen, .emojiGreen.opacity(0.8)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .cornerRadius(16)
                        .shadow(color: .emojiGreen.opacity(0.3), radius: 10, x: 0, y: 5)
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20)
                    .scaleEffect(animateElements ? 1.0 : 0.9)
                    .opacity(animateElements ? 1.0 : 0.0)
                    .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.6), value: animateElements)
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            withAnimation(.easeOut(duration: 1.0)) {
                animateElements = true
            }
        }
    }

    private var simulationHeader: some View {
        VStack(spacing: 0) {
            // Simple navigation header
            HStack {
                Button(action: { dismiss() }) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                        Text("Back")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(.white)
                }

                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            .padding(.bottom, 20)

            // Simulation title with icon
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    HStack(spacing: 12) {
                        Text(simulation.imageIcon)
                            .font(.system(size: 40))

                        VStack(alignment: .leading, spacing: 4) {
                            Text(simulation.title)
                                .font(.system(size: 28, weight: .bold))
                                .foregroundColor(.white)
                                .lineLimit(2)
                                .multilineTextAlignment(.leading)

                            Text(simulation.description)
                                .font(.system(size: 16))
                                .foregroundColor(.white.opacity(0.8))
                                .lineLimit(2)
                                .multilineTextAlignment(.leading)
                        }
                    }
                }

                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 30)
        }
    }
}

// MARK: - Supporting Models

struct CulturalSimulation {
    let id = UUID()
    let title: String
    let description: String
    let language: Language
    let category: SimulationCategory
    let difficulty: String
    let duration: String
    let participants: Int
    let scenario: String
    let learningObjectives: [String]
    let culturalNotes: [String]
    let completionRate: Double
    let rating: Double
    let imageIcon: String

    static let mockSimulations: [CulturalSimulation] = [
        // French Simulations
        CulturalSimulation(
            title: "Ordering at a Parisian Café",
            description: "Experience the art of French café culture while ordering coffee and pastries",
            language: .french,
            category: .dining,
            difficulty: "Beginner",
            duration: "15 min",
            participants: 2,
            scenario: "You're visiting a traditional Parisian café for the first time. Practice ordering, asking about menu items, and engaging in polite conversation with the waiter.",
            learningObjectives: [
                "Master café vocabulary and expressions",
                "Learn proper greeting etiquette",
                "Practice polite ordering phrases",
                "Understand French dining customs"
            ],
            culturalNotes: [
                "French cafés are social spaces for conversation",
                "It's polite to greet everyone when entering",
                "Tipping is not mandatory but appreciated",
                "Coffee is typically served strong and small"
            ],
            completionRate: 0.87,
            rating: 4.8,
            imageIcon: "☕"
        ),

        CulturalSimulation(
            title: "Shopping at a French Market",
            description: "Navigate a traditional French market and practice bargaining",
            language: .french,
            category: .shopping,
            difficulty: "Intermediate",
            duration: "20 min",
            participants: 3,
            scenario: "You're at a bustling French market looking for fresh produce and local specialties. Practice interacting with vendors and other shoppers.",
            learningObjectives: [
                "Learn market vocabulary",
                "Practice numbers and quantities",
                "Understand bargaining etiquette",
                "Cultural shopping customs"
            ],
            culturalNotes: [
                "Markets are central to French community life",
                "Quality is more important than price",
                "Vendors appreciate when you speak French",
                "Bring your own shopping bag"
            ],
            completionRate: 0.73,
            rating: 4.6,
            imageIcon: "🥖"
        ),

        // Spanish Simulations
        CulturalSimulation(
            title: "Family Dinner in Madrid",
            description: "Join a Spanish family for a traditional dinner experience",
            language: .spanish,
            category: .social,
            difficulty: "Intermediate",
            duration: "25 min",
            participants: 4,
            scenario: "You've been invited to a Spanish family's home for dinner. Navigate family conversations, meal customs, and social etiquette.",
            learningObjectives: [
                "Family vocabulary and relationships",
                "Dinner conversation skills",
                "Spanish meal customs",
                "Polite expressions and gratitude"
            ],
            culturalNotes: [
                "Spanish dinners are late (9-10 PM)",
                "Family time is sacred",
                "Expect multiple courses",
                "Conversation is lively and overlapping"
            ],
            completionRate: 0.81,
            rating: 4.9,
            imageIcon: "🥘"
        ),

        // Japanese Simulations
        CulturalSimulation(
            title: "Business Meeting in Tokyo",
            description: "Navigate Japanese business etiquette and formal meetings",
            language: .japanese,
            category: .business,
            difficulty: "Advanced",
            duration: "30 min",
            participants: 5,
            scenario: "You're attending your first business meeting in Tokyo. Learn proper greetings, meeting etiquette, and professional communication.",
            learningObjectives: [
                "Business Japanese vocabulary",
                "Formal speech patterns (keigo)",
                "Meeting etiquette and hierarchy",
                "Business card exchange ritual"
            ],
            culturalNotes: [
                "Hierarchy is extremely important",
                "Silence is acceptable and respectful",
                "Business cards are treated with reverence",
                "Consensus-building takes time"
            ],
            completionRate: 0.65,
            rating: 4.7,
            imageIcon: "🏢"
        ),

        // Tamil Simulations
        CulturalSimulation(
            title: "Tamil Wedding Celebration",
            description: "Experience the rich traditions of a Tamil wedding ceremony",
            language: .tamil,
            category: .cultural,
            difficulty: "Intermediate",
            duration: "35 min",
            participants: 6,
            scenario: "You're attending a traditional Tamil wedding. Learn about customs, participate in rituals, and interact with family members.",
            learningObjectives: [
                "Wedding vocabulary and traditions",
                "Family relationship terms",
                "Religious and cultural expressions",
                "Ceremonial participation"
            ],
            culturalNotes: [
                "Weddings are community celebrations",
                "Traditional dress is expected",
                "Multiple ceremonies over several days",
                "Food is central to the celebration"
            ],
            completionRate: 0.79,
            rating: 4.8,
            imageIcon: "💒"
        )
    ]

    static let additionalSimulations: [CulturalSimulation] = [
        // Tamil Simulations
        CulturalSimulation(
            title: "Ordering Food in Chennai",
            description: "Experience authentic Tamil dining culture while ordering traditional South Indian food",
            language: .tamil,
            category: .dining,
            difficulty: "Beginner",
            duration: "15 min",
            participants: 2,
            scenario: "You're visiting a traditional Tamil restaurant in Chennai. Practice ordering dosa, idli, and other South Indian delicacies while learning proper dining etiquette.",
            learningObjectives: [
                "Master Tamil food vocabulary",
                "Learn proper greeting customs",
                "Practice polite ordering phrases",
                "Understand Tamil dining traditions"
            ],
            culturalNotes: [
                "Tamil meals are traditionally served on banana leaves",
                "It's customary to wash hands before eating",
                "Eating with hands is the traditional way",
                "Respect for elders is very important in Tamil culture"
            ],
            completionRate: 0.85,
            rating: 4.7,
            imageIcon: "🍛"
        ),

        CulturalSimulation(
            title: "Shopping at a Tamil Market",
            description: "Navigate a bustling Tamil market and practice bargaining in Tamil",
            language: .tamil,
            category: .shopping,
            difficulty: "Intermediate",
            duration: "20 min",
            participants: 3,
            scenario: "You're at a traditional Tamil market looking for spices, vegetables, and textiles. Practice interacting with vendors and other shoppers.",
            learningObjectives: [
                "Learn market vocabulary in Tamil",
                "Practice numbers and quantities",
                "Understand bargaining customs",
                "Learn about Tamil spices and foods"
            ],
            culturalNotes: [
                "Bargaining is expected in traditional markets",
                "Building relationships with vendors is important",
                "Fresh produce is bought daily",
                "Markets are social gathering places"
            ],
            completionRate: 0.78,
            rating: 4.5,
            imageIcon: "🏪"
        ),

        // English Simulations
        CulturalSimulation(
            title: "Job Interview in London",
            description: "Practice professional English in a formal job interview setting",
            language: .english,
            category: .business,
            difficulty: "Advanced",
            duration: "25 min",
            participants: 2,
            scenario: "You're interviewing for a position at a London-based company. Practice professional communication and British business etiquette.",
            learningObjectives: [
                "Master professional vocabulary",
                "Learn British business customs",
                "Practice formal communication",
                "Understand interview protocols"
            ],
            culturalNotes: [
                "Punctuality is highly valued",
                "Firm handshakes are expected",
                "Small talk about weather is common",
                "Understatement is a British trait"
            ],
            completionRate: 0.82,
            rating: 4.6,
            imageIcon: "💼"
        ),

        // Spanish Simulations
        CulturalSimulation(
            title: "Fiesta in Barcelona",
            description: "Join a traditional Spanish celebration and learn cultural expressions",
            language: .spanish,
            category: .social,
            difficulty: "Intermediate",
            duration: "30 min",
            participants: 5,
            scenario: "You're attending a local fiesta in Barcelona. Practice social interactions, learn about Spanish traditions, and join in the celebrations.",
            learningObjectives: [
                "Learn celebration vocabulary",
                "Practice social expressions",
                "Understand Spanish traditions",
                "Learn about regional differences"
            ],
            culturalNotes: [
                "Spanish celebrations are very social",
                "Food and family are central to fiestas",
                "Dancing is an important part of culture",
                "Regional traditions vary significantly"
            ],
            completionRate: 0.89,
            rating: 4.8,
            imageIcon: "🎉"
        )
    ]
}

enum SimulationCategory: CaseIterable {
    case all, dining, shopping, social, business, cultural, travel, education

    var displayName: String {
        switch self {
        case .all: return "All"
        case .dining: return "Dining"
        case .shopping: return "Shopping"
        case .social: return "Social"
        case .business: return "Business"
        case .cultural: return "Cultural"
        case .travel: return "Travel"
        case .education: return "Education"
        }
    }
}

// MARK: - Shared Tile Components (from LessonDetailView)

struct TileItem {
    let title: String
    let subtitle: String
    let color: Color
}

struct CategoryTileSection: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let items: [TileItem]
    @State private var showingItems = false

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Category header with illustration
            HStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    Text(title)
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.white)

                    Text(subtitle)
                        .font(.system(size: 16))
                        .foregroundColor(.white.opacity(0.8))
                }

                Spacer()

                // Colorful illustration circle
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [color, color.opacity(0.7)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)

                    Image(systemName: icon)
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(.white)
                }
            }

            // Tiles grid
            if showingItems {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                    ForEach(Array(items.enumerated()), id: \.offset) { index, item in
                        TileView(item: item)
                    }
                }
                .transition(.opacity.combined(with: .scale))
            }

            // Hide/Show toggle button
            Button(action: {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    showingItems.toggle()
                }
            }) {
                HStack(spacing: 8) {
                    Image(systemName: showingItems ? "chevron.up" : "chevron.down")
                        .font(.system(size: 14, weight: .medium))
                    Text(showingItems ? "Hide \(title.lowercased())" : "Show \(title.lowercased())")
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.white.opacity(0.8))
            }
        }
        .onAppear {
            // Auto-show first category
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    showingItems = true
                }
            }
        }
    }
}

struct TileView: View {
    let item: TileItem

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(item.title)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
                .lineLimit(2)
                .multilineTextAlignment(.leading)

            if !item.subtitle.isEmpty {
                Text(item.subtitle)
                    .font(.system(size: 14))
                    .foregroundColor(.white.opacity(0.7))
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [item.color, item.color.opacity(0.8)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        )
    }
}

#Preview {
    SimulationsView()
}