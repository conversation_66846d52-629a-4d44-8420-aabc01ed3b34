//
//  SimulationsView.swift
//  NIRA
//
//  Created by NIRA Team on 27/05/2025.
//

import SwiftUI

// MARK: - Simulations View

struct SimulationsView: View {
    @State private var selectedCategory: SimulationCategory = .all
    @State private var selectedLanguage: Language = .french
    @State private var showingSimulation = false
    @State private var selectedSimulation: CulturalSimulation?

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    simulationHeaderSection

                    // Language Filter
                    languageFilterSection

                    // Category Filter
                    categoryFilterSection

                    // Simulations Grid
                    simulationsGridSection

                    // Benefits Section
                    benefitsSection
                }
                .padding()
            }
            .navigationTitle("Cultural Simulations")
            .background(
                LinearGradient(
                    colors: [Color.niraThemeTeal.opacity(0.05), Color.clear],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
        }
        .sheet(isPresented: $showingSimulation) {
            if let simulation = selectedSimulation {
                SimulationDetailView(simulation: simulation)
            }
        }
    }

    // MARK: - Header Section

    private var simulationHeaderSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Cultural Simulations")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.primaryText)

            Text("Practice real-world conversations in authentic cultural contexts")
                .font(.subheadline)
                .foregroundColor(.secondaryText)
                .multilineTextAlignment(.leading)
        }
    }

    // MARK: - Language Filter Section

    private var languageFilterSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Select Language")
                .font(.headline)
                .fontWeight(.semibold)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(Language.allCases, id: \.self) { language in
                        FilterChip(
                            title: language.displayName,
                            isSelected: selectedLanguage == language,
                            color: .niraThemeBlue,
                            action: { selectedLanguage = language }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
    }

    // MARK: - Category Filter Section

    private var categoryFilterSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Categories")
                .font(.headline)
                .fontWeight(.semibold)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(SimulationCategory.allCases, id: \.self) { category in
                        FilterChip(
                            title: category.displayName,
                            isSelected: selectedCategory == category,
                            color: .niraThemeTeal,
                            action: { selectedCategory = category }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
    }

    // MARK: - Simulations Grid Section

    private var simulationsGridSection: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 1), spacing: 16) {
            ForEach(filteredSimulations, id: \.id) { simulation in
                ModernSimulationCard(culturalSimulation: simulation) {
                    selectedSimulation = simulation
                    showingSimulation = true
                }
            }
        }
    }

    // MARK: - Benefits Section

    private var benefitsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Why Cultural Simulations?")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                BenefitRow(icon: "globe.americas.fill", title: "Real-World Context", description: "Learn language in authentic cultural situations")
                BenefitRow(icon: "person.2.fill", title: "Social Skills", description: "Practice conversations and social interactions")
                BenefitRow(icon: "brain.head.profile", title: "Cultural Intelligence", description: "Understand customs, etiquette, and social norms")
                BenefitRow(icon: "star.fill", title: "Confidence Building", description: "Gain confidence for real-world interactions")
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .emojiGreen.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }

    // MARK: - Computed Properties

    private var filteredSimulations: [CulturalSimulation] {
        let languageFiltered = CulturalSimulation.mockSimulations.filter { $0.language == selectedLanguage }

        if selectedCategory == .all {
            return languageFiltered
        } else {
            return languageFiltered.filter { $0.category == selectedCategory }
        }
    }
}

// MARK: - Supporting Views

struct SimulationCard: View {
    let simulation: CulturalSimulation
    let action: () -> Void
    @State private var isPressed = false

    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 16) {
                // Header with icon and category
                HStack {
                    Text(simulation.imageIcon)
                        .font(.system(size: 40))

                    Spacer()

                    VStack(alignment: .trailing, spacing: 4) {
                        Text(simulation.category.displayName)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(Color.emojiGreen.opacity(0.2))
                            .foregroundColor(.emojiGreen)
                            .cornerRadius(8)

                        HStack(spacing: 4) {
                            Image(systemName: "star.fill")
                                .font(.caption2)
                                .foregroundColor(.yellow)
                            Text(String(format: "%.1f", simulation.rating))
                                .font(.caption2)
                                .fontWeight(.medium)
                        }
                    }
                }

                // Title and description
                VStack(alignment: .leading, spacing: 8) {
                    Text(simulation.title)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text(simulation.description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }

                // Details row
                HStack(spacing: 16) {
                    DetailItem(icon: "clock", text: simulation.duration)
                    DetailItem(icon: "person.2", text: "\(simulation.participants) people")
                    DetailItem(icon: "chart.bar", text: simulation.difficulty)
                }

                // Progress bar
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text("Completion Rate")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Spacer()
                        Text("\(Int(simulation.completionRate * 100))%")
                            .font(.caption)
                            .fontWeight(.medium)
                    }

                    ProgressView(value: simulation.completionRate)
                        .progressViewStyle(LinearProgressViewStyle(tint: .emojiGreen))
                }

                // Start button
                HStack {
                    Image(systemName: "play.fill")
                        .font(.caption)
                    Text("Start Simulation")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(Color.emojiGreen.gradient)
                .cornerRadius(12)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .emojiGreen.opacity(isPressed ? 0.2 : 0.1), radius: isPressed ? 8 : 4, x: 0, y: isPressed ? 4 : 2)
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

struct DetailItem: View {
    let icon: String
    let text: String

    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.emojiGreen)
            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

struct BenefitRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.emojiGreen)
                .frame(width: 24, height: 24)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primaryText)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondaryText)
            }

            Spacer()
        }
    }
}

struct SimulationDetailView: View {
    let simulation: CulturalSimulation
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Header
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Text(simulation.imageIcon)
                                .font(.system(size: 50))

                            Spacer()

                            VStack(alignment: .trailing, spacing: 4) {
                                Text(simulation.category.displayName)
                                    .font(.caption)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 2)
                                    .background(Color.emojiGreen.opacity(0.2))
                                    .foregroundColor(.emojiGreen)
                                    .cornerRadius(8)

                                HStack(spacing: 4) {
                                    Image(systemName: "star.fill")
                                        .font(.caption)
                                        .foregroundColor(.yellow)
                                    Text(String(format: "%.1f", simulation.rating))
                                        .font(.caption)
                                        .fontWeight(.medium)
                                }
                            }
                        }

                        Text(simulation.title)
                            .font(.title2)
                            .fontWeight(.bold)

                        Text(simulation.description)
                            .font(.body)
                            .foregroundColor(.secondary)
                    }

                    // Scenario
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Scenario")
                            .font(.headline)
                            .fontWeight(.semibold)

                        Text(simulation.scenario)
                            .font(.body)
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(12)
                    }

                    // Learning Objectives
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Learning Objectives")
                            .font(.headline)
                            .fontWeight(.semibold)

                        ForEach(simulation.learningObjectives, id: \.self) { objective in
                            HStack(alignment: .top, spacing: 8) {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.emojiGreen)
                                    .font(.caption)

                                Text(objective)
                                    .font(.subheadline)
                            }
                        }
                    }

                    // Cultural Notes
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Cultural Notes")
                            .font(.headline)
                            .fontWeight(.semibold)

                        ForEach(simulation.culturalNotes, id: \.self) { note in
                            HStack(alignment: .top, spacing: 8) {
                                Image(systemName: "lightbulb.fill")
                                    .foregroundColor(.orange)
                                    .font(.caption)

                                Text(note)
                                    .font(.subheadline)
                            }
                        }
                    }

                    // Start Button
                    Button(action: {
                        // TODO: Start simulation
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        HStack {
                            Image(systemName: "play.fill")
                            Text("Start Simulation")
                                .fontWeight(.semibold)
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.emojiGreen.gradient)
                        .cornerRadius(12)
                    }
                }
                .padding()
            }
            .navigationTitle("Simulation Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Supporting Models

struct CulturalSimulation {
    let id = UUID()
    let title: String
    let description: String
    let language: Language
    let category: SimulationCategory
    let difficulty: String
    let duration: String
    let participants: Int
    let scenario: String
    let learningObjectives: [String]
    let culturalNotes: [String]
    let completionRate: Double
    let rating: Double
    let imageIcon: String

    static let mockSimulations: [CulturalSimulation] = [
        // French Simulations
        CulturalSimulation(
            title: "Ordering at a Parisian Café",
            description: "Experience the art of French café culture while ordering coffee and pastries",
            language: .french,
            category: .dining,
            difficulty: "Beginner",
            duration: "15 min",
            participants: 2,
            scenario: "You're visiting a traditional Parisian café for the first time. Practice ordering, asking about menu items, and engaging in polite conversation with the waiter.",
            learningObjectives: [
                "Master café vocabulary and expressions",
                "Learn proper greeting etiquette",
                "Practice polite ordering phrases",
                "Understand French dining customs"
            ],
            culturalNotes: [
                "French cafés are social spaces for conversation",
                "It's polite to greet everyone when entering",
                "Tipping is not mandatory but appreciated",
                "Coffee is typically served strong and small"
            ],
            completionRate: 0.87,
            rating: 4.8,
            imageIcon: "☕"
        ),

        CulturalSimulation(
            title: "Shopping at a French Market",
            description: "Navigate a traditional French market and practice bargaining",
            language: .french,
            category: .shopping,
            difficulty: "Intermediate",
            duration: "20 min",
            participants: 3,
            scenario: "You're at a bustling French market looking for fresh produce and local specialties. Practice interacting with vendors and other shoppers.",
            learningObjectives: [
                "Learn market vocabulary",
                "Practice numbers and quantities",
                "Understand bargaining etiquette",
                "Cultural shopping customs"
            ],
            culturalNotes: [
                "Markets are central to French community life",
                "Quality is more important than price",
                "Vendors appreciate when you speak French",
                "Bring your own shopping bag"
            ],
            completionRate: 0.73,
            rating: 4.6,
            imageIcon: "🥖"
        ),

        // Spanish Simulations
        CulturalSimulation(
            title: "Family Dinner in Madrid",
            description: "Join a Spanish family for a traditional dinner experience",
            language: .spanish,
            category: .social,
            difficulty: "Intermediate",
            duration: "25 min",
            participants: 4,
            scenario: "You've been invited to a Spanish family's home for dinner. Navigate family conversations, meal customs, and social etiquette.",
            learningObjectives: [
                "Family vocabulary and relationships",
                "Dinner conversation skills",
                "Spanish meal customs",
                "Polite expressions and gratitude"
            ],
            culturalNotes: [
                "Spanish dinners are late (9-10 PM)",
                "Family time is sacred",
                "Expect multiple courses",
                "Conversation is lively and overlapping"
            ],
            completionRate: 0.81,
            rating: 4.9,
            imageIcon: "🥘"
        ),

        // Japanese Simulations
        CulturalSimulation(
            title: "Business Meeting in Tokyo",
            description: "Navigate Japanese business etiquette and formal meetings",
            language: .japanese,
            category: .business,
            difficulty: "Advanced",
            duration: "30 min",
            participants: 5,
            scenario: "You're attending your first business meeting in Tokyo. Learn proper greetings, meeting etiquette, and professional communication.",
            learningObjectives: [
                "Business Japanese vocabulary",
                "Formal speech patterns (keigo)",
                "Meeting etiquette and hierarchy",
                "Business card exchange ritual"
            ],
            culturalNotes: [
                "Hierarchy is extremely important",
                "Silence is acceptable and respectful",
                "Business cards are treated with reverence",
                "Consensus-building takes time"
            ],
            completionRate: 0.65,
            rating: 4.7,
            imageIcon: "🏢"
        ),

        // Tamil Simulations
        CulturalSimulation(
            title: "Tamil Wedding Celebration",
            description: "Experience the rich traditions of a Tamil wedding ceremony",
            language: .tamil,
            category: .cultural,
            difficulty: "Intermediate",
            duration: "35 min",
            participants: 6,
            scenario: "You're attending a traditional Tamil wedding. Learn about customs, participate in rituals, and interact with family members.",
            learningObjectives: [
                "Wedding vocabulary and traditions",
                "Family relationship terms",
                "Religious and cultural expressions",
                "Ceremonial participation"
            ],
            culturalNotes: [
                "Weddings are community celebrations",
                "Traditional dress is expected",
                "Multiple ceremonies over several days",
                "Food is central to the celebration"
            ],
            completionRate: 0.79,
            rating: 4.8,
            imageIcon: "💒"
        )
    ]
}

enum SimulationCategory: CaseIterable {
    case all, dining, shopping, social, business, cultural, travel, education

    var displayName: String {
        switch self {
        case .all: return "All"
        case .dining: return "Dining"
        case .shopping: return "Shopping"
        case .social: return "Social"
        case .business: return "Business"
        case .cultural: return "Cultural"
        case .travel: return "Travel"
        case .education: return "Education"
        }
    }
}

#Preview {
    SimulationsView()
}