import SwiftUI
import Foundation

// MARK: - Lesson Detail View with Modern Design

struct LessonDetailView: View {
    let lesson: SupabaseLesson
    @Environment(\.dismiss) private var dismiss
    @State private var selectedSection: LessonSection = .overview
    @State private var showingExercises = false
    @State private var animateElements = false

    enum LessonSection: String, CaseIterable {
        case overview = "Overview"
        case vocabulary = "Vocabulary"
        case dialogues = "Dialogues"
        case grammar = "Grammar"
        case exercises = "Exercises"
        case audio = "Audio"

        var icon: String {
            switch self {
            case .overview: return "book.fill"
            case .vocabulary: return "textformat.abc"
            case .dialogues: return "message.fill"
            case .grammar: return "text.book.closed.fill"
            case .exercises: return "pencil.circle.fill"
            case .audio: return "speaker.wave.2.fill"
            }
        }

        var color: Color {
            switch self {
            case .overview: return .blue
            case .vocabulary: return .green
            case .dialogues: return .purple
            case .grammar: return .orange
            case .exercises: return .red
            case .audio: return .pink
            }
        }
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Premium Background
                LinearGradient(
                    colors: [
                        Color(.systemBackground),
                        Color(.systemGray6).opacity(0.3),
                        Color(.systemBackground)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Premium Header
                    lessonHeader

                    // Section Navigation
                    sectionNavigationView

                    // Content Area
                    ScrollView {
                        VStack(spacing: 24) {
                            selectedSectionContent
                        }
                        .padding()
                    }

                    // Action Button
                    actionButton
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            withAnimation(.easeOut(duration: 1.0)) {
                animateElements = true
            }
        }
    }

    // MARK: - Header

    private var lessonHeader: some View {
        VStack(spacing: 16) {
            // Navigation and Actions
            HStack {
                Button(action: { dismiss() }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.primary)
                        .padding(12)
                        .background(Color(.systemBackground))
                        .cornerRadius(12)
                        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                }

                Spacer()

                Button(action: {}) {
                    Image(systemName: "bookmark")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.primary)
                        .padding(12)
                        .background(Color(.systemBackground))
                        .cornerRadius(12)
                        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)

            // Lesson Info Card
            VStack(spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 8) {
                        Text(lesson.title)
                            .font(.system(size: 24, weight: .bold))
                            .foregroundColor(.primary)
                            .lineLimit(2)

                        if let description = lesson.description {
                            Text(description)
                                .font(.system(size: 16))
                                .foregroundColor(.secondary)
                                .lineLimit(3)
                        }
                    }

                    Spacer()

                    // Difficulty Badge
                    VStack(spacing: 4) {
                        Text(lesson.difficultyText)
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(lesson.difficultyColor)
                            .cornerRadius(12)

                        Text("Level")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }

                // Stats Row
                HStack(spacing: 20) {
                    LessonStatItem(icon: "book", value: "\(lesson.vocabularyFocus?.count ?? 0)", label: "Words")
                    LessonStatItem(icon: "message", value: "0", label: "Dialogues")
                    LessonStatItem(icon: "pencil", value: "\(lesson.exercises.count)", label: "Exercises")
                    LessonStatItem(icon: "clock", value: "\(lesson.estimatedDuration ?? 15) min", label: "Duration")
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
            )
            .padding(.horizontal, 20)
            .scaleEffect(animateElements ? 1.0 : 0.9)
            .opacity(animateElements ? 1.0 : 0.0)
            .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.2), value: animateElements)
        }
        .padding(.bottom, 20)
    }

    // MARK: - Section Navigation

    private var sectionNavigationView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(LessonSection.allCases, id: \.self) { section in
                    SectionChip(
                        section: section,
                        isSelected: selectedSection == section,
                        action: {
                            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                selectedSection = section
                            }
                        }
                    )
                }
            }
            .padding(.horizontal, 20)
        }
        .padding(.bottom, 20)
    }

    // MARK: - Content Sections

    @ViewBuilder
    private var selectedSectionContent: some View {
        switch selectedSection {
        case .overview:
            overviewSection
        case .vocabulary:
            vocabularySection
        case .dialogues:
            dialoguesSection
        case .grammar:
            grammarSection
        case .exercises:
            exercisesSection
        case .audio:
            audioSection
        }
    }

    private var overviewSection: some View {
        VStack(spacing: 20) {
            PremiumContentCard(
                title: "Lesson Overview",
                icon: "book.fill",
                color: .blue
            ) {
                VStack(alignment: .leading, spacing: 12) {
                    Text(lesson.description ?? "This lesson will help you learn essential language skills.")
                        .font(.body)
                        .foregroundColor(.primary)

                    if let vocabularyFocus = lesson.vocabularyFocus, !vocabularyFocus.isEmpty {
                        Text("Key Topics:")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .padding(.top, 8)

                        ForEach(vocabularyFocus.prefix(3), id: \.self) { word in
                            HStack {
                                Circle()
                                    .fill(Color.blue.opacity(0.2))
                                    .frame(width: 6, height: 6)
                                Text(word)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                Spacer()
                            }
                        }
                    }
                }
            }
        }
    }

    private var vocabularySection: some View {
        VStack(spacing: 16) {
            if let vocabularyFocus = lesson.vocabularyFocus {
                ForEach(vocabularyFocus, id: \.self) { word in
                    LessonVocabularyCard(
                        word: word,
                        translation: nil, // Would need to fetch from vocabulary table
                        pronunciation: nil
                    )
                }
            } else {
                Text("No vocabulary items available")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .padding()
            }
        }
    }

    private var dialoguesSection: some View {
        VStack(spacing: 16) {
            // Sample dialogues since SupabaseLesson doesn't have dialogues
            LessonDialogueCard(
                speaker: "Teacher",
                text: "Sample dialogue content would go here",
                translation: "This is where the translation would appear"
            )

            Text("Dialogue content would be loaded from separate table")
                .font(.caption)
                .foregroundColor(.secondary)
                .italic()
        }
    }

    private var grammarSection: some View {
        PremiumContentCard(
            title: "Grammar Points",
            icon: "text.book.closed.fill",
            color: .orange
        ) {
            Text("Grammar content will be displayed here")
                .font(.body)
                .foregroundColor(.secondary)
        }
    }

    private var exercisesSection: some View {
        VStack(spacing: 16) {
            ForEach(lesson.exercises, id: \.id) { exercise in
                LessonExerciseCard(exercise: exercise)
            }

            if lesson.exercises.isEmpty {
                Text("No exercises available for this lesson")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .padding()
            }
        }
    }

    private var audioSection: some View {
        PremiumContentCard(
            title: "Audio Practice",
            icon: "speaker.wave.2.fill",
            color: .pink
        ) {
            if lesson.hasAudio == true {
                Text("Audio content available")
                    .font(.body)
                    .foregroundColor(.primary)
            } else {
                Text("No audio content available for this lesson")
                    .font(.body)
                    .foregroundColor(.secondary)
            }
        }
    }

    // MARK: - Action Button

    private var actionButton: some View {
        Button(action: {
            showingExercises = true
        }) {
            HStack(spacing: 12) {
                Image(systemName: "play.fill")
                    .font(.system(size: 16, weight: .semibold))

                Text("Start Learning")
                    .font(.system(size: 18, weight: .semibold))
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity, minHeight: 56)
            .background(
                LinearGradient(
                    colors: [Color.niraPrimary, Color.niraPrimary.opacity(0.8)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .cornerRadius(16)
            .shadow(color: Color.niraPrimary.opacity(0.3), radius: 10, x: 0, y: 5)
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
        .scaleEffect(animateElements ? 1.0 : 0.9)
        .opacity(animateElements ? 1.0 : 0.0)
        .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.6), value: animateElements)
    }
}

// MARK: - Supporting Components

struct LessonStatItem: View {
    let icon: String
    let value: String
    let label: String

    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.niraPrimary)

            Text(value)
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(.primary)

            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

struct SectionChip: View {
    let section: LessonDetailView.LessonSection
    let isSelected: Bool
    let action: () -> Void
    @State private var isPressed = false

    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: section.icon)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(isSelected ? .white : section.color)

                Text(section.rawValue)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(isSelected ? .white : .primary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(
                Group {
                    if isSelected {
                        section.color
                    } else {
                        Color(.systemBackground)
                    }
                }
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        isSelected ? Color.clear : section.color.opacity(0.3),
                        lineWidth: 1
                    )
            )
            .cornerRadius(16)
            .shadow(
                color: isSelected ? section.color.opacity(0.3) : Color.black.opacity(0.05),
                radius: isSelected ? 6 : 2,
                x: 0,
                y: isSelected ? 3 : 1
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
            .animation(.spring(response: 0.6, dampingFraction: 0.8), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

struct PremiumContentCard<Content: View>: View {
    let title: String
    let icon: String
    let color: Color
    let content: () -> Content

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(color)
                    .frame(width: 40, height: 40)
                    .background(color.opacity(0.1))
                    .cornerRadius(12)

                Text(title)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)

                Spacer()
            }

            content()
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

struct LessonVocabularyCard: View {
    let word: String
    let translation: String?
    let pronunciation: String?

    var body: some View {
        HStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 4) {
                Text(word)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)

                if let translation = translation {
                    Text(translation)
                        .font(.system(size: 16))
                        .foregroundColor(.secondary)
                }

                if let pronunciation = pronunciation {
                    Text("[\(pronunciation)]")
                        .font(.system(size: 14))
                        .foregroundColor(.green)
                        .italic()
                }
            }

            Spacer()

            Button(action: {}) {
                Image(systemName: "speaker.wave.2.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.blue)
                    .padding(12)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(12)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6).opacity(0.5))
        )
    }
}

struct LessonDialogueCard: View {
    let speaker: String
    let text: String
    let translation: String?

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(speaker)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.purple)

                Spacer()

                Button(action: {}) {
                    Image(systemName: "speaker.wave.2.fill")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.purple)
                }
            }

            Text(text)
                .font(.system(size: 16))
                .foregroundColor(.primary)

            if let translation = translation {
                Text(translation)
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .italic()
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.purple.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.purple.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

struct LessonExerciseCard: View {
    let exercise: SupabaseExercise

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(exercise.question)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.primary)

            if let options = exercise.options, !options.isEmpty {
                VStack(spacing: 8) {
                    ForEach(Array(options.enumerated()), id: \.offset) { index, option in
                        HStack {
                            Text("\(index + 1).")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.red)

                            Text(option)
                                .font(.system(size: 14))
                                .foregroundColor(.primary)

                            Spacer()
                        }
                        .padding(.vertical, 4)
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.red.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.red.opacity(0.2), lineWidth: 1)
                )
        )
    }
}
