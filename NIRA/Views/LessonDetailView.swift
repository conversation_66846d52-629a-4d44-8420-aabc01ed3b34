import SwiftUI
import Foundation

// MARK: - Lesson Detail View with Modern Design

struct LessonDetailView: View {
    let lesson: SupabaseLesson
    @Environment(\.dismiss) private var dismiss
    @State private var animateElements = false
    @StateObject private var userPreferences = UserPreferencesService.shared

    // Extracted content from JSON
    @State private var extractedVocabulary: [LessonVocabularyItem] = []
    @State private var extractedDialogues: [LessonDialogueItem] = []
    @State private var extractedExercises: [LessonExerciseItem] = []
    @State private var extractedGrammarPoints: [LessonGrammarPoint] = []

    // Level-based colors for this lesson
    private var levelColor: Color {
        return Color.getLevelColor(for: lesson.difficultyLevel ?? 1)
    }

    private var levelGradient: [Color] {
        return Color.getLevelGradient(for: lesson.difficultyLevel ?? 1)
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Dark background like in the reference images
                Color.black
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Header with back button and title
                    lessonHeader

                    // Main content with tile-based categories
                    ScrollView {
                        LazyVStack(spacing: 24) {
                            // Vocabulary Category
                            if !extractedVocabulary.isEmpty {
                                CategoryTileSection(
                                    title: "Vocabulary Practice",
                                    subtitle: "Learn essential words and phrases",
                                    icon: "book.fill",
                                    color: .emojiGreen,
                                    items: extractedVocabulary.map { vocab in
                                        TileItem(
                                            title: vocab.word,
                                            subtitle: vocab.translation ?? "",
                                            color: .emojiGreen
                                        )
                                    }
                                )
                            }

                            // Dialogues Category
                            if !extractedDialogues.isEmpty {
                                CategoryTileSection(
                                    title: "Guided Conversations",
                                    subtitle: "Practice real-world dialogues",
                                    icon: "message.fill",
                                    color: .emojiPurple,
                                    items: extractedDialogues.map { dialogue in
                                        TileItem(
                                            title: dialogue.text,
                                            subtitle: dialogue.translation ?? "",
                                            color: .emojiPurple
                                        )
                                    }
                                )
                            }

                            // Grammar Category
                            if !extractedGrammarPoints.isEmpty {
                                CategoryTileSection(
                                    title: "Grammar Essentials",
                                    subtitle: "Master language structure",
                                    icon: "textformat.abc",
                                    color: .emojiBlue,
                                    items: extractedGrammarPoints.map { grammar in
                                        TileItem(
                                            title: grammar.rule,
                                            subtitle: grammar.explanation,
                                            color: .emojiBlue
                                        )
                                    }
                                )
                            }

                            // Exercises Category
                            if !extractedExercises.isEmpty {
                                CategoryTileSection(
                                    title: "Practice Exercises",
                                    subtitle: "Test your knowledge",
                                    icon: "pencil.circle.fill",
                                    color: .emojiOrange,
                                    items: extractedExercises.map { exercise in
                                        TileItem(
                                            title: exercise.question,
                                            subtitle: exercise.type.capitalized,
                                            color: .emojiOrange
                                        )
                                    }
                                )
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 16)
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            withAnimation(.easeOut(duration: 1.0)) {
                animateElements = true
            }
            extractContentFromMetadata()
        }
    }

    // MARK: - Header

    private var lessonHeader: some View {
        VStack(spacing: 0) {
            // Simple navigation header
            HStack {
                Button(action: { dismiss() }) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                        Text("Back")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(.white)
                }

                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            .padding(.bottom, 20)

            // Lesson title
            HStack {
                Text(lesson.title)
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(.white)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)

                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 30)
        }
    }





    // MARK: - Content Extraction

    private func extractContentFromMetadata() {
        guard let contentMetadata = lesson.contentMetadata?.value as? [String: Any] else {
            print("❌ No content metadata found")
            return
        }

        // Extract vocabulary
        if let vocabularyArray = contentMetadata["vocabulary"] as? [[String: Any]] {
            extractedVocabulary = vocabularyArray.compactMap { vocabDict in
                guard let word = vocabDict["word"] as? String else { return nil }
                return LessonVocabularyItem(
                    word: word,
                    translation: vocabDict["translation"] as? String,
                    pronunciation: vocabDict["pronunciation"] as? String,
                    partOfSpeech: vocabDict["part_of_speech"] as? String,
                    example: vocabDict["example"] as? String
                )
            }
        }

        // Extract dialogues
        if let dialoguesArray = contentMetadata["dialogues"] as? [[String: Any]] {
            extractedDialogues = dialoguesArray.compactMap { dialogueDict in
                guard let text = dialogueDict["text"] as? String,
                      let speaker = dialogueDict["speaker"] as? String else { return nil }
                return LessonDialogueItem(
                    speaker: speaker,
                    text: text,
                    translation: dialogueDict["translation"] as? String,
                    culturalNote: dialogueDict["culturalNote"] as? String
                )
            }
        }

        // Extract exercises
        if let exercisesArray = contentMetadata["exercises"] as? [[String: Any]] {
            extractedExercises = exercisesArray.compactMap { exerciseDict in
                guard let question = exerciseDict["question"] as? String,
                      let type = exerciseDict["type"] as? String else { return nil }
                return LessonExerciseItem(
                    type: type,
                    question: question,
                    options: exerciseDict["options"] as? [String] ?? [],
                    correctAnswer: exerciseDict["correct_answer"] as? Int ?? 0,
                    explanation: exerciseDict["explanation"] as? String,
                    points: exerciseDict["points"] as? Int ?? 10
                )
            }
        }

        // Extract grammar points
        if let grammarArray = contentMetadata["grammar_points"] as? [[String: Any]] {
            extractedGrammarPoints = grammarArray.compactMap { grammarDict in
                guard let rule = grammarDict["rule"] as? String else { return nil }
                return LessonGrammarPoint(
                    rule: rule,
                    explanation: grammarDict["explanation"] as? String ?? "",
                    examples: grammarDict["examples"] as? [String] ?? [],
                    tips: grammarDict["tips"] as? String
                )
            }
        }

        print("✅ Extracted content: \(extractedVocabulary.count) vocab, \(extractedDialogues.count) dialogues, \(extractedExercises.count) exercises")
    }
}

// MARK: - Supporting Components

struct LessonStatItem: View {
    let icon: String
    let value: String
    let label: String
    let color: Color

    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(color)

            Text(value)
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(.primary)

            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}



struct PremiumContentCard<Content: View>: View {
    let title: String
    let icon: String
    let color: Color
    let content: () -> Content

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(color)
                    .frame(width: 40, height: 40)
                    .background(color.opacity(0.1))
                    .cornerRadius(12)

                Text(title)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)

                Spacer()
            }

            content()
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

struct LessonVocabularyCard: View {
    let word: String
    let translation: String?
    let pronunciation: String?

    var body: some View {
        HStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 4) {
                Text(word)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)

                if let translation = translation {
                    Text(translation)
                        .font(.system(size: 16))
                        .foregroundColor(.secondary)
                }

                if let pronunciation = pronunciation {
                    Text("[\(pronunciation)]")
                        .font(.system(size: 14))
                        .foregroundColor(.green)
                        .italic()
                }
            }

            Spacer()

            Button(action: {}) {
                Image(systemName: "speaker.wave.2.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.blue)
                    .padding(12)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(12)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6).opacity(0.5))
        )
    }
}

struct LessonDialogueCard: View {
    let speaker: String
    let text: String
    let translation: String?

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(speaker)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.purple)

                Spacer()

                Button(action: {}) {
                    Image(systemName: "speaker.wave.2.fill")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.purple)
                }
            }

            Text(text)
                .font(.system(size: 16))
                .foregroundColor(.primary)

            if let translation = translation {
                Text(translation)
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .italic()
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.purple.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.purple.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

struct LessonExerciseCard: View {
    let exercise: SupabaseExercise

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(exercise.question)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.primary)

            if let options = exercise.options, !options.isEmpty {
                VStack(spacing: 8) {
                    ForEach(Array(options.enumerated()), id: \.offset) { index, option in
                        HStack {
                            Text("\(index + 1).")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.red)

                            Text(option)
                                .font(.system(size: 14))
                                .foregroundColor(.primary)

                            Spacer()
                        }
                        .padding(.vertical, 4)
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.red.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.red.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

// MARK: - Data Models for Extracted Content

struct LessonVocabularyItem {
    let word: String
    let translation: String?
    let pronunciation: String?
    let partOfSpeech: String?
    let example: String?
}

struct LessonDialogueItem {
    let speaker: String
    let text: String
    let translation: String?
    let culturalNote: String?
}

struct LessonExerciseItem {
    let type: String
    let question: String
    let options: [String]
    let correctAnswer: Int
    let explanation: String?
    let points: Int
}

struct LessonGrammarPoint {
    let rule: String
    let explanation: String
    let examples: [String]
    let tips: String?
}

struct ExtractedExerciseCard: View {
    let exercise: LessonExerciseItem
    let index: Int

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Exercise \(index)")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.red)
                    .cornerRadius(8)

                Spacer()

                Text("\(exercise.points) pts")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.red)
            }

            Text(exercise.question)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.primary)

            if !exercise.options.isEmpty {
                VStack(spacing: 8) {
                    ForEach(Array(exercise.options.enumerated()), id: \.offset) { index, option in
                        HStack {
                            Text("\(index + 1).")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.red)
                                .frame(width: 20, alignment: .leading)

                            Text(option)
                                .font(.system(size: 14))
                                .foregroundColor(.primary)

                            Spacer()

                            if index == exercise.correctAnswer {
                                Image(systemName: "checkmark.circle.fill")
                                    .font(.system(size: 14))
                                    .foregroundColor(.green)
                            }
                        }
                        .padding(.vertical, 4)
                    }
                }
            }

            if let explanation = exercise.explanation {
                Text("💡 \(explanation)")
                    .font(.system(size: 13))
                    .foregroundColor(.secondary)
                    .italic()
                    .padding(.top, 8)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.red.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.red.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

// MARK: - New Tile-Based Components

struct TileItem {
    let title: String
    let subtitle: String
    let color: Color
}

struct CategoryTileSection: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let items: [TileItem]
    @State private var showingItems = false

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Category header with illustration
            HStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    Text(title)
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.white)

                    Text(subtitle)
                        .font(.system(size: 16))
                        .foregroundColor(.white.opacity(0.8))
                }

                Spacer()

                // Colorful illustration circle
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [color, color.opacity(0.7)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)

                    Image(systemName: icon)
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(.white)
                }
            }

            // Tiles grid
            if showingItems {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                    ForEach(Array(items.enumerated()), id: \.offset) { index, item in
                        TileView(item: item)
                    }
                }
                .transition(.opacity.combined(with: .scale))
            }

            // Hide/Show toggle button
            Button(action: {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    showingItems.toggle()
                }
            }) {
                HStack(spacing: 8) {
                    Image(systemName: showingItems ? "chevron.up" : "chevron.down")
                        .font(.system(size: 14, weight: .medium))
                    Text(showingItems ? "Hide \(title.lowercased())" : "Show \(title.lowercased())")
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.white.opacity(0.8))
            }
        }
        .onAppear {
            // Auto-show first category
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    showingItems = true
                }
            }
        }
    }
}

struct TileView: View {
    let item: TileItem

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(item.title)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
                .lineLimit(2)
                .multilineTextAlignment(.leading)

            if !item.subtitle.isEmpty {
                Text(item.subtitle)
                    .font(.system(size: 14))
                    .foregroundColor(.white.opacity(0.7))
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [item.color, item.color.opacity(0.8)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        )
    }
}

#Preview {
    let sampleLesson = SupabaseLesson(
        id: UUID(),
        pathId: UUID(),
        title: "Saying how you're doing",
        description: "Learn essential phrases for expressing how you feel",
        lessonType: "conversation",
        difficultyLevel: 2,
        estimatedDuration: 25,
        sequenceOrder: 1,
        learningObjectives: ["Express feelings", "Use common phrases", "Practice pronunciation"],
        vocabularyFocus: ["good", "fine", "tired", "happy"],
        grammarConcepts: ["Present tense"],
        culturalNotes: "Different cultures express feelings differently",
        prerequisiteLessons: [],
        contentMetadata: SupabaseAnyCodable([
            "vocabulary": [
                [
                    "word": "good",
                    "translation": "bien",
                    "pronunciation": "bee-en"
                ],
                [
                    "word": "tired",
                    "translation": "cansado",
                    "pronunciation": "kan-sa-do"
                ]
            ],
            "dialogues": [
                [
                    "speaker": "Maria",
                    "text": "How are you doing?",
                    "translation": "¿Cómo estás?"
                ],
                [
                    "speaker": "Juan",
                    "text": "I'm doing well, thanks",
                    "translation": "Estoy bien, gracias"
                ]
            ],
            "exercises": [
                [
                    "type": "multiple_choice",
                    "question": "How do you say 'good' in Spanish?",
                    "options": ["bien", "mal", "así", "todo"]
                ]
            ],
            "grammar_points": [
                [
                    "rule": "Present tense of 'estar'",
                    "explanation": "Used to express temporary states"
                ]
            ]
        ]),
        isActive: true,
        createdAt: Date(),
        updatedAt: Date(),
        audioUrl: nil,
        hasAudio: false,
        audioMetadata: nil
    )

    LessonDetailView(lesson: sampleLesson)
        .preferredColorScheme(.dark)
}