import SwiftUI
import Foundation

struct SimulationBrowserView: View {
    @StateObject private var simulationService = SimulationService()
    @StateObject private var userPreferences = UserPreferencesService.shared
    @State private var showingSimulationPlayer = false
    @State private var selectedSimulation: Simulation?
    @State private var selectedLevel: String = "All"
    @State private var searchText = ""

    // Same meaningful level names as lessons page
    private let levels = ["All", "Beginner Foundations", "Elementary Essentials", "Intermediate Conversations", "Advanced Communication", "Professional Mastery", "Expert Fluency"]

    // Mapping from display names to CEFR levels for database queries
    private let levelMapping: [String: String] = [
        "All": "All",
        "Beginner Foundations": "A1",
        "Elementary Essentials": "A2",
        "Intermediate Conversations": "B1",
        "Advanced Communication": "B2",
        "Professional Mastery": "C1",
        "Expert Fluency": "C2"
    ]

    // Use user's selected language instead of separate state
    private var selectedLanguage: Language {
        userPreferences.selectedLanguage
    }

    var body: some View {
        VStack(spacing: 0) {
            // Modern Header
            ModernSimulationsHeader(
                selectedLanguage: selectedLanguage.displayName,
                searchText: $searchText
            )

            // Filter Controls
            filterControlsView

            // Main content
            if filteredSimulations.isEmpty {
                loadingOrEmptyState
            } else {
                mainContentView
            }
        }
        .background(Color(.systemGroupedBackground))
        .navigationBarHidden(true)
        .onAppear {
            print("🔍 SimulationBrowserView: View appeared")
            loadInitialData()
        }
        .sheet(isPresented: $showingSimulationPlayer) {
            if selectedSimulation != nil {
                // For now, we'll need to create a default persona or modify SimulationPlayerView
                // to work without personas. This is a temporary solution.
                Text("Simulation Player - Coming Soon")
                    .font(.title2)
                    .padding()
            }
        }
    }

    // MARK: - Filter Controls

    private var filterControlsView: some View {
        VStack(spacing: 16) {
            // Modern Level Filter with Premium Design (same as lessons page)
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(levels, id: \.self) { level in
                        PremiumLevelChip(
                            title: level,
                            isSelected: selectedLevel == level,
                            action: {
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    selectedLevel = level
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(.vertical, 16)
        .background(
            LinearGradient(
                colors: [Color(.systemBackground), Color(.systemGray6).opacity(0.3)],
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }

    // MARK: - Main Content

    private var mainContentView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(filteredSimulations, id: \.id) { simulation in
                    BrowserSimulationCard(
                        simulation: simulation,
                        simulationService: simulationService
                    ) {
                        selectedSimulation = simulation
                        showingSimulationPlayer = true
                    }
                    .transition(.scale.combined(with: .opacity))
                }
            }
            .padding()
        }
        .animation(.easeInOut, value: filteredSimulations.count)
    }



    // MARK: - Loading/Empty State

    private var loadingOrEmptyState: some View {
        VStack(spacing: 20) {
            Image(systemName: "theatermasks")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            Text("No simulations available")
                .font(.headline)
                .foregroundColor(.secondary)
            Text("Check back later for new content")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // MARK: - Computed Properties

    private var filteredSimulations: [Simulation] {
        var filtered = simulationService.currentSimulations

        // Filter by search text
        if !searchText.isEmpty {
            filtered = filtered.filter { simulation in
                simulation.title.localizedCaseInsensitiveContains(searchText) ||
                simulation.description.localizedCaseInsensitiveContains(searchText) ||
                simulation.scenarioType.localizedCaseInsensitiveContains(searchText) ||
                (simulation.vocabularyFocus?.contains { vocab in
                    vocab.word.localizedCaseInsensitiveContains(searchText) ||
                    vocab.translation.localizedCaseInsensitiveContains(searchText)
                } ?? false)
            }
        }

        // Filter by level using the mapping (same as lessons page)
        if selectedLevel != "All" {
            if let cefrLevel = levelMapping[selectedLevel] {
                filtered = filtered.filter { simulation in
                    simulation.difficultyLevel.uppercased() == cefrLevel
                }
            }
        }

        // Sort by difficulty level first (A1 first), then by creation date (same as lessons page)
        return filtered.sorted { simulation1, simulation2 in
            let level1 = getDifficultyNumber(for: simulation1.difficultyLevel.uppercased())
            let level2 = getDifficultyNumber(for: simulation2.difficultyLevel.uppercased())

            if level1 != level2 {
                return level1 < level2 // A1 (1) comes before A2 (2), etc.
            }

            // If same level, sort by creation date (newest first)
            return simulation1.createdAt > simulation2.createdAt
        }
    }

    // MARK: - Helper Functions

    private func getDifficultyNumber(for level: String) -> Int {
        switch level {
        case "A1": return 1
        case "A2": return 2
        case "B1": return 3
        case "B2": return 4
        case "C1": return 5
        case "C2": return 6
        default: return 1
        }
    }

    // MARK: - Helper Methods

    private func loadInitialData() {
        // Language is now automatically synced with user preferences
        let language = selectedLanguage
        let languageId = getLanguageId(for: language)

        print("🔍 SimulationBrowserView: Loading initial data for language: \(language.displayName)")

        // Load all simulations for the selected language
        simulationService.loadAllSimulations(for: languageId)
    }

    private func getCurrentUserId() -> UUID? {
        guard let userIdString = UserDefaults.standard.string(forKey: "user_id"),
              let userId = UUID(uuidString: userIdString) else { return nil }
        return userId
    }

    private func getLanguageId(for language: Language) -> UUID {
        // Updated mapping to match actual database UUIDs
        switch language {
        case .french: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295641") ?? UUID()
        case .english: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295642") ?? UUID()
        case .spanish: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295643") ?? UUID()
        case .tamil: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295644") ?? UUID()
        case .japanese: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295645") ?? UUID()
        case .korean: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295646") ?? UUID()
        case .italian: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295647") ?? UUID()
        case .german: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295648") ?? UUID()
        case .hindi: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295649") ?? UUID()
        case .chinese: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295650") ?? UUID()
        case .portuguese: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295651") ?? UUID()
        case .telugu: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295652") ?? UUID()
        case .vietnamese: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295653") ?? UUID()
        case .indonesian: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295654") ?? UUID()
        case .arabic: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295655") ?? UUID()
        // New 10 languages
        case .kannada: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295656") ?? UUID()
        case .malayalam: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295657") ?? UUID()
        case .bengali: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295658") ?? UUID()
        case .marathi: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295659") ?? UUID()
        case .punjabi: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295660") ?? UUID()
        case .dutch: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295661") ?? UUID()
        case .swedish: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295662") ?? UUID()
        case .thai: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295663") ?? UUID()
        case .russian: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295664") ?? UUID()
        case .norwegian: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295665") ?? UUID()
        // Additional 25 languages
        case .gujarati: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295666") ?? UUID()
        case .odia: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295667") ?? UUID()
        case .assamese: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295668") ?? UUID()
        case .konkani: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295669") ?? UUID()
        case .sindhi: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295670") ?? UUID()
        case .bhojpuri: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295671") ?? UUID()
        case .maithili: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295672") ?? UUID()
        case .swahili: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295673") ?? UUID()
        case .hebrew: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295674") ?? UUID()
        case .greek: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295675") ?? UUID()
        case .turkish: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295676") ?? UUID()
        case .farsi: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295677") ?? UUID()
        case .tagalog: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295678") ?? UUID()
        case .ukrainian: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295679") ?? UUID()
        case .danish: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295680") ?? UUID()
        case .xhosa: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295681") ?? UUID()
        case .zulu: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295682") ?? UUID()
        case .amharic: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295683") ?? UUID()
        case .quechua: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295684") ?? UUID()
        case .maori: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295685") ?? UUID()
        case .cherokee: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295686") ?? UUID()
        case .navajo: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295687") ?? UUID()
        case .hawaiian: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295688") ?? UUID()
        case .inuktitut: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295689") ?? UUID()
        case .yoruba: return UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295690") ?? UUID()
        default:
            // Default UUID for new languages - in production, these would have proper UUIDs
            return UUID()
        }
    }
}

// MARK: - Supporting Views

struct DifficultyLevelChip: View {
    let title: String
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            Text(title)
                .font(.subheadline)
                .fontWeight(isSelected ? .semibold : .regular)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(isSelected ? Color.blue : Color(.systemGray6))
                .foregroundColor(isSelected ? .white : .primary)
                .cornerRadius(20)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct PersonaCard: View {
    let persona: SimulationPersona
    let isSelected: Bool
    let simulationService: SimulationService
    let onTap: () -> Void
    let onDetailTap: () -> Void

    var body: some View {
        VStack(spacing: 12) {
            // Icon and selection indicator
            ZStack {
                Circle()
                    .fill(Color(hex: simulationService.getPersonaColor(persona.colorTheme)))
                    .frame(width: 60, height: 60)

                Text(simulationService.getPersonaIcon(persona.iconName))
                    .font(.title)

                if isSelected {
                    Circle()
                        .stroke(Color.blue, lineWidth: 3)
                        .frame(width: 66, height: 66)
                }
            }

            VStack(spacing: 4) {
                Text(persona.displayName)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)

                Text(persona.difficultyRange.capitalized)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            // Detail button
            Button("Learn More") {
                onDetailTap()
            }
            .font(.caption)
            .foregroundColor(.blue)
        }
        .frame(width: 120)
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(radius: isSelected ? 4 : 2)
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isSelected)
        .onTapGesture {
            onTap()
        }
    }
}

struct BrowserSimulationCard: View {
    let simulation: Simulation
    let simulationService: SimulationService
    let onTap: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header with difficulty and duration
            HStack {
                Text(simulation.difficultyLevel.uppercased())
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.blue)

                Spacer()

                HStack(spacing: 4) {
                    Image(systemName: "clock")
                        .font(.caption)
                    Text("\(simulation.estimatedDuration)m")
                        .font(.caption)
                }
                .foregroundColor(.secondary)
            }

            // Title
            Text(simulation.title)
                .font(.subheadline)
                .fontWeight(.semibold)
                .lineLimit(2)
                .multilineTextAlignment(.leading)

            // Description
            Text(simulation.description)
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(3)
                .multilineTextAlignment(.leading)

            Spacer()

            // Start button
            Button("Start Simulation") {
                onTap()
            }
            .font(.caption)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 8)
            .background(Color.blue)
            .cornerRadius(8)
        }
        .padding()
        .frame(height: 180)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct QuickStatsView: View {
    let userId: UUID
    let simulationService: SimulationService
    @State private var stats: [String: Any] = [:]

    var body: some View {
        HStack(spacing: 20) {
            StatItem(
                title: "Completed",
                value: "\(stats["completed_simulations"] as? Int ?? 0)",
                color: .green
            )

            StatItem(
                title: "Mastered",
                value: "\(stats["mastered_simulations"] as? Int ?? 0)",
                color: .blue
            )

            StatItem(
                title: "Avg Score",
                value: "\(Int(stats["average_score"] as? Double ?? 0))%",
                color: .orange
            )
        }
        .onAppear {
            loadStats()
        }
    }

    private func loadStats() {
        Task {
            stats = await simulationService.getSimulationStats(userId: userId)
        }
    }
}

struct StatItem: View {
    let title: String
    let value: String
    let color: Color

    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(color)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

struct RecommendationsSection: View {
    let userId: UUID
    let language: Language
    let simulationService: SimulationService
    let onSimulationSelected: (Simulation) -> Void

    @State private var recommendations: [Simulation] = []

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Recommended for You")
                    .font(.title2)
                    .fontWeight(.bold)

                Spacer()

                Button("Refresh") {
                    loadRecommendations()
                }
                .font(.caption)
                .foregroundColor(.blue)
            }

            if recommendations.isEmpty {
                Text("Complete more simulations to get personalized recommendations")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 16) {
                        ForEach(recommendations, id: \.id) { simulation in
                            BrowserRecommendationCard(simulation: simulation) {
                                onSimulationSelected(simulation)
                            }
                        }
                    }
                    .padding(.horizontal)
                }
            }
        }
        .onAppear {
            loadRecommendations()
        }
    }

    private func loadRecommendations() {
        Task {
            // Use a hardcoded Tamil UUID for now since we can't access the main getLanguageId function
            let tamilId = UUID(uuidString: "4df28de4-168f-4d8e-a304-b785e9295644") ?? UUID()
            recommendations = await simulationService.getRecommendedSimulations(
                for: userId,
                languageId: tamilId
            )
        }
    }
}

struct BrowserRecommendationCard: View {
    let simulation: Simulation
    let onTap: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(simulation.title)
                .font(.subheadline)
                .fontWeight(.semibold)
                .lineLimit(2)

            Text(simulation.description)
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(3)

            Spacer()

            Button("Try Now") {
                onTap()
            }
            .font(.caption)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 6)
            .background(Color.blue)
            .cornerRadius(8)
        }
        .padding()
        .frame(width: 160, height: 120)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct PersonaDetailView: View {
    let persona: SimulationPersona
    let simulationService: SimulationService
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    personaHeaderView
                    personaDescriptionView
                    personaTargetAudienceView
                    personaLearningObjectivesView
                    personaTypicalScenariosView
                    personaVocabularyFocusView
                }
                .padding()
            }
            .navigationTitle("Persona Details")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }

    // MARK: - Persona Detail Views

    private var personaHeaderView: some View {
        VStack(spacing: 16) {
            Circle()
                .fill(Color(hex: simulationService.getPersonaColor(persona.colorTheme)))
                .frame(width: 100, height: 100)
                .overlay(
                    Text(simulationService.getPersonaIcon(persona.iconName))
                        .font(.system(size: 40))
                )

            VStack(spacing: 8) {
                Text(persona.displayName)
                    .font(.title)
                    .fontWeight(.bold)

                Text(persona.difficultyRange.capitalized)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(hex: simulationService.getPersonaColor(persona.colorTheme)).opacity(0.1))
        .cornerRadius(16)
    }

    private var personaDescriptionView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("About This Persona")
                .font(.headline)
                .fontWeight(.semibold)

            Text(persona.description)
                .font(.body)
        }
    }

    private var personaTargetAudienceView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Perfect For")
                .font(.headline)
                .fontWeight(.semibold)

            Text(persona.targetAudience)
                .font(.body)
        }
    }

    @ViewBuilder
    private var personaLearningObjectivesView: some View {
        if !(persona.learningObjectives?.isEmpty ?? true) {
            VStack(alignment: .leading, spacing: 12) {
                Text("Learning Objectives")
                    .font(.headline)
                    .fontWeight(.semibold)

                ForEach(persona.learningObjectives ?? [], id: \.self) { objective in
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                        Text(objective)
                            .font(.body)
                        Spacer()
                    }
                }
            }
        }
    }

    @ViewBuilder
    private var personaTypicalScenariosView: some View {
        if !(persona.typicalScenarios?.isEmpty ?? true) {
            VStack(alignment: .leading, spacing: 12) {
                Text("Typical Scenarios")
                    .font(.headline)
                    .fontWeight(.semibold)

                ForEach(persona.typicalScenarios ?? [], id: \.self) { scenario in
                    HStack {
                        Image(systemName: "location.circle")
                            .foregroundColor(Color(hex: simulationService.getPersonaColor(persona.colorTheme)))
                        Text(scenario)
                            .font(.body)
                        Spacer()
                    }
                }
            }
        }
    }

    @ViewBuilder
    private var personaVocabularyFocusView: some View {
        if !(persona.vocabularyFocus?.isEmpty ?? true) {
            VStack(alignment: .leading, spacing: 12) {
                Text("Vocabulary Focus")
                    .font(.headline)
                    .fontWeight(.semibold)

                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                    ForEach(persona.vocabularyFocus ?? [], id: \.self) { word in
                        Text(word.replacingOccurrences(of: "_", with: " ").capitalized)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color(hex: simulationService.getPersonaColor(persona.colorTheme)).opacity(0.2))
                            .cornerRadius(8)
                    }
                }
            }
        }
    }
}

// MARK: - Additional Supporting Views

struct ScenarioTypeChip: View {
    let title: String
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            Text(title)
                .font(.subheadline)
                .fontWeight(isSelected ? .semibold : .regular)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(isSelected ? Color.green : Color(.systemGray6))
                .foregroundColor(isSelected ? .white : .primary)
                .cornerRadius(20)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct SimulationListRow: View {
    let simulation: Simulation
    let simulationService: SimulationService
    let onTap: () -> Void

    var body: some View {
        HStack(spacing: 16) {
            // Scenario Type Icon
            Circle()
                .fill(Color.blue.opacity(0.1))
                .frame(width: 50, height: 50)
                .overlay(
                    Image(systemName: getScenarioIcon(simulation.scenarioType))
                        .font(.title3)
                        .foregroundColor(.blue)
                )

            // Content
            VStack(alignment: .leading, spacing: 4) {
                Text(simulation.title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .lineLimit(1)

                Text(simulation.description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(2)

                HStack {
                    Text(simulation.difficultyLevel.uppercased())
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)

                    Text("•")
                        .foregroundColor(.secondary)

                    Text("\(simulation.estimatedDuration)m")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text("•")
                        .foregroundColor(.secondary)

                    Text(simulation.scenarioType.capitalized.replacingOccurrences(of: "_", with: " "))
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()
                }
            }

            Spacer()

            // Action Button
            Button("Start") {
                onTap()
            }
            .font(.caption)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(Color.blue)
            .cornerRadius(8)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }

    private func getScenarioIcon(_ scenarioType: String) -> String {
        switch scenarioType {
        case "daily_life": return "house"
        case "shopping": return "cart"
        case "transportation": return "car"
        case "work": return "briefcase"
        case "health": return "heart"
        case "education": return "book"
        case "social": return "person.2"
        case "technology": return "laptopcomputer"
        case "cultural": return "globe"
        case "problem_solving": return "lightbulb"
        default: return "bubble.left.and.bubble.right"
        }
    }
}


