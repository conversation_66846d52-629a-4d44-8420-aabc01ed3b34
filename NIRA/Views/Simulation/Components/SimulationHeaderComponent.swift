//
//  SimulationHeaderComponent.swift
//  NIRA
//
//  Created by NIRA Team on 2025-01-29.
//

import SwiftUI

struct SimulationHeaderComponent: View {
    let simulation: Simulation
    let persona: SimulationPersona
    let timeSpent: TimeInterval
    let currentScore: Double
    let onDismiss: () -> Void

    var body: some View {
        HStack {
            Button(action: onDismiss) {
                Image(systemName: "xmark.circle.fill")
                    .font(.title2)
                    .foregroundColor(.gray)
            }
            .accessibilityLabel("Close simulation")
            .accessibilityHint("Returns to previous screen")

            VStack(alignment: .leading, spacing: 4) {
                Text(simulation.title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .accessibilityAddTraits(.isHeader)

                HStack {
                    Text(getPersonaIcon(persona.iconName))
                        .font(.caption)
                        .accessibilityHidden(true)
                    Text(persona.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .accessibilityElement(children: .combine)
                .accessibilityLabel("Simulation persona: \(persona.displayName)")
            }

            Spacer()

            VStack(alignment: .trailing, spacing: 4) {
                Text("\(formatTime(timeSpent))")
                    .font(.caption)
                    .fontWeight(.medium)
                    .accessibilityLabel("Time spent: \(formatTime(timeSpent))")

                Text("Score: \(Int(currentScore))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .accessibilityLabel("Current score: \(Int(currentScore)) percent")
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .shadow(radius: 1)
    }

    private func formatTime(_ timeInterval: TimeInterval) -> String {
        let minutes = Int(timeInterval) / 60
        let seconds = Int(timeInterval) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    private func getPersonaIcon(_ iconName: String) -> String {
        switch iconName {
        case "airplane": return "✈️"
        case "home": return "🏠"
        case "briefcase": return "💼"
        case "graduation": return "🎓"
        case "family": return "👨‍👩‍👧‍👦"
        case "medical": return "🏥"
        case "job": return "💼"
        case "social": return "🤝"
        default: return "👤"
        }
    }
}

#Preview {
    SimulationHeaderComponent(
        simulation: Simulation(
            id: UUID(),
            personaId: UUID(),
            languageId: UUID(),
            title: "Sample Simulation",
            description: "Sample Description",
            difficultyLevel: "beginner",
            estimatedDuration: 15,
            scenarioType: "conversation",
            learningObjectives: ["Practice conversation"],
            vocabularyFocus: nil as [String]?,
            conversationStarters: nil as [String]?,
            successCriteria: nil as [String]?,
            culturalNotes: nil as String?,
            isActive: true,
            createdAt: Date(),
            updatedAt: Date(),
            realLifeContext: nil as String?,
            realisticDialogue: nil as String?,
            conversationBranches: nil as [String]?,
            aiAgentIntegration: nil as String?,
            audioIntegration: nil as String?,
            culturalDeepDive: nil as String?,
            prerequisiteLessons: nil as [String]?,
            followUpLessons: nil as [String]?,
            practiceSuggestions: nil as [String]?,
            gamificationElements: nil as [String]?,
            realLifeReadinessScore: nil as Double?
        ),
        persona: SimulationPersona(
            id: UUID(),
            name: "sample",
            displayName: "Sample Persona",
            description: "Sample",
            targetAudience: "Sample",
            difficultyRange: "A1-B2",
            colorTheme: "#007AFF",
            iconName: "person",
            learningObjectives: nil as [String]?,
            typicalScenarios: nil as [String]?,
            vocabularyFocus: nil as [String]?,
            personalityTraits: nil as [String: SupabaseAnyCodable]?,
            teachingStyle: nil as String?,
            isActive: true,
            sortOrder: 1,
            createdAt: Date(),
            updatedAt: Date()
        ),
        timeSpent: 120,
        currentScore: 85.0,
        onDismiss: {}
    )
}
