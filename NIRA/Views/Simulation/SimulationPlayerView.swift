import SwiftUI
import AVFoundation

struct SimulationPlayerView: View {
    let simulation: Simulation
    let persona: SimulationPersona
    @StateObject private var simulationService = SimulationService()
    @StateObject private var speechService = SpeechService()
    @State private var currentDialogueIndex = 0
    @State private var dialogues: [SimulationDialogue] = []
    @State private var userResponses: [String] = []
    @State private var currentScore = 0.0
    @State private var timeSpent = 0
    @State private var isLoading = true
    @State private var showingCompletion = false
    @State private var selectedResponse: String?
    @State private var showingFeedback = false
    @State private var feedbackText = ""
    @State private var isRecording = false
    @State private var recordedText = ""

    @Environment(\.dismiss) private var dismiss

    private let timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()

    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient based on persona theme
                LinearGradient(
                    colors: [
                        Color(hex: simulationService.getPersonaColor(persona.colorTheme)).opacity(0.1),
                        Color(hex: simulationService.getPersonaColor(persona.colorTheme)).opacity(0.05)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()

                if isLoading {
                    LoadingView()
                } else {
                    VStack(spacing: 0) {
                        // Header
                        simulationHeader

                        // Progress Bar
                        progressBar

                        // Main Content
                        ScrollView {
                            VStack(spacing: 20) {
                                // Scenario Context
                                scenarioCard

                                // Current Dialogue
                                if currentDialogueIndex < dialogues.count {
                                    dialogueCard
                                }

                                // Response Options or Input
                                responseSection

                                // Cultural Notes
                                if let currentDialogue = getCurrentDialogue(),
                                   let culturalContext = currentDialogue.culturalContext,
                                   !culturalContext.isEmpty {
                                    culturalNotesCard(culturalContext)
                                }
                            }
                            .padding()
                        }

                        // Bottom Controls
                        bottomControls
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            loadSimulationData()
        }
        .onReceive(timer) { _ in
            timeSpent += 1
        }
        .sheet(isPresented: $showingCompletion) {
            SimulationCompletionView(
                simulation: simulation,
                persona: persona,
                finalScore: currentScore,
                timeSpent: timeSpent,
                onDismiss: { dismiss() }
            )
        }
        .alert("Feedback", isPresented: $showingFeedback) {
            Button("Continue") { proceedToNext() }
        } message: {
            Text(feedbackText)
        }
    }

    // MARK: - Header

    private var simulationHeader: some View {
        HStack {
            Button(action: { dismiss() }) {
                Image(systemName: "xmark.circle.fill")
                    .font(.title2)
                    .foregroundColor(.gray)
            }
            .accessibilityLabel("Close simulation")
            .accessibilityHint("Returns to previous screen")

            VStack(alignment: .leading, spacing: 4) {
                Text(simulation.title)
                    .font(.headline)
                    .fontWeight(.semibold)

                HStack {
                    Text(simulationService.getPersonaIcon(persona.iconName))
                        .font(.caption)
                    Text(persona.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            Spacer()

            VStack(alignment: .trailing, spacing: 4) {
                Text("\(formatTime(timeSpent))")
                    .font(.caption)
                    .fontWeight(.medium)

                Text("Score: \(Int(currentScore))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .shadow(radius: 1)
    }

    // MARK: - Progress Bar

    private var progressBar: some View {
        ProgressView(value: Double(currentDialogueIndex), total: Double(max(dialogues.count, 1)))
            .progressViewStyle(LinearProgressViewStyle(tint: Color(hex: simulationService.getPersonaColor(persona.colorTheme))))
            .padding(.horizontal)
    }

    // MARK: - Scenario Card

    private var scenarioCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "location.circle.fill")
                    .foregroundColor(Color(hex: simulationService.getPersonaColor(persona.colorTheme)))
                Text("Scenario")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }

            // Enhanced real-life context
            if let realLifeContext = simulation.realLifeContext {
                VStack(alignment: .leading, spacing: 8) {
                    if let situationValue = realLifeContext["situation"]?.value,
                       let situation = situationValue as? String {
                        Text(situation)
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.leading)
                    }

                    if let locationValue = realLifeContext["location"]?.value,
                       let location = locationValue as? String {
                        HStack {
                            Image(systemName: "location")
                                .foregroundColor(.blue)
                                .font(.caption)
                            Text(location)
                                .font(.caption)
                                .foregroundColor(.blue)
                            Spacer()
                        }
                    }

                    if let culturalContextValue = realLifeContext["cultural_context"]?.value,
                       let culturalContext = culturalContextValue as? String {
                        HStack {
                            Image(systemName: "globe")
                                .foregroundColor(.orange)
                                .font(.caption)
                            Text(culturalContext)
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                        }
                    }
                }
            } else {
                // Fallback to description
                Text(simulation.description)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
            }

            // Learning Objectives
            if !simulation.learningObjectives.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Learning Objectives")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    ForEach(simulation.learningObjectives, id: \.self) { objective in
                        HStack {
                            Image(systemName: "checkmark.circle")
                                .foregroundColor(.green)
                                .font(.caption)
                            Text(objective)
                                .font(.caption)
                            Spacer()
                        }
                    }
                }
                .padding(.top, 8)
            }

            // Enhanced Cultural Context
            if let culturalDeepDive = simulation.culturalDeepDive {
                VStack(alignment: .leading, spacing: 12) {
                    Text("Cultural Context")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    if let etiquetteRulesValue = culturalDeepDive["etiquette_rules"]?.value,
                       let etiquetteRules = etiquetteRulesValue as? [String], !etiquetteRules.isEmpty {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Etiquette Tips:")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.orange)

                            ForEach(etiquetteRules.prefix(2), id: \.self) { rule in
                                HStack {
                                    Image(systemName: "lightbulb")
                                        .foregroundColor(.orange)
                                        .font(.caption2)
                                    Text(rule)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    Spacer()
                                }
                            }
                        }
                    }

                    if let commonMistakesValue = culturalDeepDive["common_mistakes"]?.value,
                       let commonMistakes = commonMistakesValue as? [String], !commonMistakes.isEmpty {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Avoid These Mistakes:")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.red)

                            ForEach(commonMistakes.prefix(2), id: \.self) { mistake in
                                HStack {
                                    Image(systemName: "exclamationmark.triangle")
                                        .foregroundColor(.red)
                                        .font(.caption2)
                                    Text(mistake)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    Spacer()
                                }
                            }
                        }
                    }
                }
                .padding(.top, 8)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    // MARK: - Dialogue Card

    private var dialogueCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            if let dialogue = getCurrentDialogue() {
                // Speaker Info
                HStack {
                    Circle()
                        .fill(dialogue.speakerRole == "user" ? Color.blue : Color.green)
                        .frame(width: 40, height: 40)
                        .overlay(
                            Text(dialogue.speakerRole == "user" ? "You" : (dialogue.speakerName?.prefix(1) ?? "N").uppercased())
                                .font(.caption)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        )

                    VStack(alignment: .leading, spacing: 2) {
                        Text(dialogue.speakerRole == "user" ? "You" : dialogue.speakerName ?? "NPC")
                            .font(.subheadline)
                            .fontWeight(.semibold)

                        Text(dialogue.speakerRole.capitalized)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    // Audio Button
                    if dialogue.audioUrl != nil {
                        Button(action: { playAudio(dialogue.audioUrl!) }) {
                            Image(systemName: "speaker.wave.2.fill")
                                .foregroundColor(Color(hex: simulationService.getPersonaColor(persona.colorTheme)))
                        }
                    }
                }

                // Dialogue Text
                VStack(alignment: .leading, spacing: 8) {
                    Text(dialogue.dialogueText)
                        .font(.body)
                        .padding()
                        .background(
                            dialogue.speakerRole == "user" ?
                            Color.blue.opacity(0.1) :
                            Color(.systemGray6)
                        )
                        .cornerRadius(12)

                    // Translation (if available)
                    if let translation = dialogue.dialogueTranslation {
                        Text(translation)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .italic()
                    }
                }

                // Vocabulary Highlights
                if !dialogue.vocabularyHighlights.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Key Vocabulary")
                            .font(.caption)
                            .fontWeight(.medium)

                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                            ForEach(dialogue.vocabularyHighlights, id: \.self) { word in
                                Text(word)
                                    .font(.caption)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color(hex: simulationService.getPersonaColor(persona.colorTheme)).opacity(0.2))
                                    .cornerRadius(8)
                            }
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }

    // MARK: - Response Section

    private var responseSection: some View {
        VStack(spacing: 16) {
            if let dialogue = getCurrentDialogue(), !dialogue.responseOptions.isEmpty {
                // Multiple Choice Responses
                VStack(spacing: 12) {
                    Text("Choose your response:")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    ForEach(dialogue.responseOptions, id: \.id) { option in
                        Button(action: { selectResponse(option) }) {
                            HStack {
                                Text(option.text)
                                    .font(.body)
                                    .multilineTextAlignment(.leading)
                                Spacer()
                                if selectedResponse == option.id {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(.green)
                                }
                            }
                            .padding()
                            .background(
                                selectedResponse == option.id ?
                                Color.green.opacity(0.1) :
                                Color(.systemGray6)
                            )
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(
                                        selectedResponse == option.id ?
                                        Color.green :
                                        Color.clear,
                                        lineWidth: 2
                                    )
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
            } else {
                // Voice Input Section
                VStack(spacing: 12) {
                    Text("Speak your response:")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    Button(action: { toggleRecording() }) {
                        VStack {
                            Image(systemName: isRecording ? "stop.circle.fill" : "mic.circle.fill")
                                .font(.system(size: 60))
                                .foregroundColor(isRecording ? .red : Color(hex: simulationService.getPersonaColor(persona.colorTheme)))

                            Text(isRecording ? "Tap to stop recording" : "Tap to start recording")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .buttonStyle(PlainButtonStyle())

                    if !recordedText.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Your response:")
                                .font(.caption)
                                .fontWeight(.medium)

                            Text(recordedText)
                                .font(.body)
                                .padding()
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(12)
                        }
                    }
                }
            }

            // AI Agent Help Button
            if let aiAgentIntegration = simulation.aiAgentIntegration {
                Button(action: { showAIAgentHelp() }) {
                    HStack {
                        Image(systemName: "brain")
                            .foregroundColor(.purple)
                        Text("Get AI Help")
                            .font(.subheadline)
                            .foregroundColor(.purple)
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(Color.purple.opacity(0.1))
                    .cornerRadius(20)
                }
                .buttonStyle(PlainButtonStyle())
            }

            // Continue Button
            if selectedResponse != nil || !recordedText.isEmpty {
                Button(action: { processResponse() }) {
                    Text("Continue")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color(hex: simulationService.getPersonaColor(persona.colorTheme)))
                        .cornerRadius(12)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    // MARK: - Cultural Notes Card

    private func culturalNotesCard(_ notes: String) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "globe")
                    .foregroundColor(.orange)
                Text("Cultural Insight")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                Spacer()
            }

            Text(notes)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color.orange.opacity(0.1))
        .cornerRadius(12)
    }

    // MARK: - Bottom Controls

    private var bottomControls: some View {
        HStack {
            Button("Previous") {
                if currentDialogueIndex > 0 {
                    currentDialogueIndex -= 1
                    selectedResponse = nil
                    recordedText = ""
                }
            }
            .disabled(currentDialogueIndex == 0)

            Spacer()

            Text("\(currentDialogueIndex + 1) of \(dialogues.count)")
                .font(.caption)
                .foregroundColor(.secondary)

            Spacer()

            Button("Skip") {
                proceedToNext()
            }
            .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemBackground))
    }

    // MARK: - Helper Methods

    private func loadSimulationData() {
        Task {
            print("🎭 SimulationPlayerView: Loading simulation data for \(simulation.title)")
            print("🎭 SimulationPlayerView: Simulation ID: \(simulation.id)")

            // Load enhanced realistic dialogues
            if let realisticDialogue = simulation.realisticDialogue, !realisticDialogue.isEmpty {
                print("🎭 SimulationPlayerView: Found realistic dialogue with \(realisticDialogue.count) entries")
                dialogues = convertRealisticDialogue(realisticDialogue)
                print("🎭 SimulationPlayerView: Converted to \(dialogues.count) dialogue objects")
            } else {
                print("🎭 SimulationPlayerView: No realistic dialogue found, using fallback")
                // Fallback to traditional dialogue loading
                dialogues = await simulationService.loadDialogues(for: simulation.id)
                print("🎭 SimulationPlayerView: Loaded \(dialogues.count) dialogues from service")

                // If still no dialogues, create a sample one for testing
                if dialogues.isEmpty {
                    print("🎭 SimulationPlayerView: Creating sample dialogue for testing")
                    dialogues = [
                        SimulationDialogue(
                            id: UUID(),
                            simulationId: simulation.id,
                            sequenceOrder: 1,
                            speakerRole: "npc",
                            speakerName: "Host",
                            dialogueText: "Welcome! How can I help you today?",
                            dialogueTranslation: "Translation will be available soon",
                            audioUrl: nil,
                            responseOptions: [
                                ResponseOption(
                                    id: "sample_1",
                                    text: "I'd like to practice this conversation",
                                    translation: "I'd like to practice this conversation",
                                    isCorrect: true,
                                    culturalAppropriate: true,
                                    nextSequence: nil,
                                    feedback: "Great choice!"
                                )
                            ],
                            culturalContext: "This is a sample conversation for testing",
                            vocabularyHighlights: ["welcome", "help"],
                            grammarNotes: nil,
                            difficultyLevel: simulation.difficultyLevel,
                            isCriticalPath: true,
                            createdAt: Date()
                        )
                    ]
                }
            }

            print("🎭 SimulationPlayerView: Final dialogue count: \(dialogues.count)")
            isLoading = false

            // Start simulation tracking
            if let userId = UserDefaults.standard.string(forKey: "user_id"),
               let userUUID = UUID(uuidString: userId) {
                await simulationService.startSimulation(simulation, userId: userUUID)
            }
        }
    }

    private func convertRealisticDialogue(_ realisticDialogue: [[String: SupabaseAnyCodable]]) -> [SimulationDialogue] {
        var convertedDialogues: [SimulationDialogue] = []
        print("🎭 Converting realistic dialogue with \(realisticDialogue.count) entries")

        for (index, dialogueData) in realisticDialogue.enumerated() {
            let speaker = dialogueData["speaker"]?.value as? String ?? "Speaker"
            let text = dialogueData["text"]?.value as? String ?? ""
            let translation = dialogueData["translation"]?.value as? String
            let audioNotes = dialogueData["audio_notes"]?.value as? String
            let culturalNotes = dialogueData["cultural_notes"]?.value as? String

            print("🎭 Converting dialogue \(index): speaker=\(speaker), text=\(text.prefix(50))...")

            // Create response options for user turns
            var responseOptions: [ResponseOption] = []
            if let optionsValue = dialogueData["response_options"]?.value,
               let options = optionsValue as? [String] {
                responseOptions = options.enumerated().map { (optionIndex, optionText) in
                    ResponseOption(
                        id: "\(index)_\(optionIndex)",
                        text: optionText,
                        translation: optionText, // Add translation
                        isCorrect: optionIndex == 0, // First option is typically correct
                        culturalAppropriate: true,
                        nextSequence: nil, // Add nextSequence
                        feedback: "Good choice! This response is culturally appropriate."
                    )
                }
            }

            let dialogue = SimulationDialogue(
                id: UUID(uuidString: "\(simulation.id)_\(index)") ?? UUID(),
                simulationId: simulation.id,
                sequenceOrder: index,
                speakerRole: speaker.lowercased().contains("user") ? "user" : "npc",
                speakerName: speaker,
                dialogueText: text,
                dialogueTranslation: translation,
                audioUrl: nil, // Will be populated when audio is implemented
                responseOptions: responseOptions,
                culturalContext: culturalNotes,
                vocabularyHighlights: extractVocabulary(from: text),
                grammarNotes: nil,
                difficultyLevel: "B1",
                isCriticalPath: true,
                createdAt: Date()
            )

            convertedDialogues.append(dialogue)
        }

        return convertedDialogues
    }

    private func extractVocabulary(from text: String) -> [String] {
        // Simple vocabulary extraction - in practice, this would be more sophisticated
        let words = text.components(separatedBy: .whitespacesAndNewlines)
        return words.filter { $0.count > 4 }.prefix(3).map { $0 }
    }

    private func showAIAgentHelp() {
        // Show AI agent guidance based on current context
        if let aiAgentIntegration = simulation.aiAgentIntegration,
           let agentRoleValue = aiAgentIntegration["agent_role"]?.value,
           let agentRole = agentRoleValue as? String {

            let helpMessage = "AI Agent (\(agentRole)): I'm here to help you with cultural context and pronunciation. What would you like guidance on?"

            // In a real implementation, this would show a modal or sidebar with AI assistance
            print("AI Agent Help: \(helpMessage)")

            // For now, show a simple alert
            // In production, this would integrate with the actual AI agent system
        }
    }

    private func getCurrentDialogue() -> SimulationDialogue? {
        guard currentDialogueIndex < dialogues.count else { return nil }
        return dialogues[currentDialogueIndex]
    }

    private func selectResponse(_ option: ResponseOption) {
        selectedResponse = option.id

        // Provide immediate feedback if available
        if let feedback = option.feedback {
            feedbackText = feedback
            showingFeedback = true
        }

        // Update score based on correctness
        if option.isCorrect {
            currentScore += 10
        }
        if option.culturalAppropriate {
            currentScore += 5
        }
    }

    private func toggleRecording() {
        if isRecording {
            stopRecording()
        } else {
            startRecording()
        }
    }

    private func startRecording() {
        isRecording = true
        speechService.startRecording()

        // Monitor for transcription updates
        Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { timer in
            Task { @MainActor in
                if !speechService.isRecording {
                    recordedText = speechService.transcribedText
                    isRecording = false
                    timer.invalidate()
                }
            }
        }
    }

    private func stopRecording() {
        isRecording = false
        speechService.stopRecording()
    }

    private func processResponse() {
        // Process the selected response or recorded text
        let response = selectedResponse ?? recordedText
        userResponses.append(response)

        // Update progress
        if let userId = UserDefaults.standard.string(forKey: "user_id"),
           let userUUID = UUID(uuidString: userId) {
            Task {
                await simulationService.updateProgress(
                    simulationId: simulation.id,
                    userId: userUUID,
                    dialogueChoice: response,
                    timeSpent: 1,
                    scores: ["performance": currentScore]
                )
            }
        }

        proceedToNext()
    }

    private func proceedToNext() {
        if currentDialogueIndex < dialogues.count - 1 {
            currentDialogueIndex += 1
            selectedResponse = nil
            recordedText = ""
        } else {
            completeSimulation()
        }
    }

    private func completeSimulation() {
        // Calculate final scores
        let finalScores = [
            "performance": currentScore,
            "cultural": currentScore * 0.8, // Simplified calculation
            "vocabulary": currentScore * 0.9,
            "grammar": currentScore * 0.7,
            "fluency": currentScore * 0.6
        ]

        // Update completion status
        if let userId = UserDefaults.standard.string(forKey: "user_id"),
           let userUUID = UUID(uuidString: userId) {
            Task {
                await simulationService.completeSimulation(
                    simulationId: simulation.id,
                    userId: userUUID,
                    finalScores: finalScores
                )
            }
        }

        showingCompletion = true
    }

    private func playAudio(_ url: String) {
        // Implement audio playback
        // This would use AVAudioPlayer or similar
        print("Playing audio: \(url)")
    }

    private func formatTime(_ seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%02d:%02d", minutes, remainingSeconds)
    }
}

// MARK: - Supporting Views

struct LoadingView: View {
    var body: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            Text("Loading simulation...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
}

struct SimulationCompletionView: View {
    let simulation: Simulation
    let persona: SimulationPersona
    let finalScore: Double
    let timeSpent: Int
    let onDismiss: () -> Void

    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // Success Animation
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.green)

                VStack(spacing: 16) {
                    Text("Simulation Complete!")
                        .font(.title)
                        .fontWeight(.bold)

                    Text(simulation.title)
                        .font(.headline)
                        .foregroundColor(.secondary)
                }

                // Score Summary
                VStack(spacing: 16) {
                    ScoreCard(title: "Final Score", value: "\(Int(finalScore))%", color: .blue)
                    ScoreCard(title: "Time Spent", value: formatTime(timeSpent), color: .green)
                    ScoreCard(title: "Persona", value: persona.displayName, color: .purple)
                }

                Spacer()

                Button("Continue Learning") {
                    onDismiss()
                }
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .cornerRadius(12)
                .padding(.horizontal)
            }
            .padding()
            .navigationTitle("Results")
            .navigationBarTitleDisplayMode(.inline)
        }
    }

    private func formatTime(_ seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%02d:%02d", minutes, remainingSeconds)
    }
}

struct ScoreCard: View {
    let title: String
    let value: String
    let color: Color

    var body: some View {
        HStack {
            VStack(alignment: .leading) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                Text(value)
                    .font(.title2)
                    .fontWeight(.semibold)
            }
            Spacer()
            Circle()
                .fill(color)
                .frame(width: 12, height: 12)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Color Extension

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}