//
//  HomeView.swift
//  NIRA
//
//  Created by NIRA Team on 1/2025.
//

import SwiftUI

// MARK: - Home View

struct HomeView: View {
    @Binding var selectedTab: Int
    @State private var currentStreak = 7
    @StateObject private var userPreferences = UserPreferencesService.shared
    @StateObject private var dashboardCoordinator = DashboardCoordinatorService.shared
    @Environment(\.colorScheme) var colorScheme
    @State private var showingCelebration = false
    @State private var particleAnimation = false

    var body: some View {
        NavigationView {
            ZStack {
                // Dynamic gradient background based on time
                dynamicBackgroundGradient
                    .ignoresSafeArea()

                ScrollView {
                    VStack(alignment: .leading, spacing: 24) {
                        // Animated Welcome Header with Glassmorphism
                        GlassmorphicWelcomeHeaderView(
                            streak: currentStreak,
                            userName: "Alex",
                            showCelebration: $showingCelebration
                        )

                        // Daily Goal Progress with Particles
                        ConnectedDailyGoalView(
                            goalData: dashboardCoordinator.todaysGoalData,
                            particleAnimation: $particleAnimation,
                            onGoalTap: {
                                // Navigate to lessons for selected language
                                selectedTab = 1 // Switch to Lessons tab
                            }
                        )

                        // Language Selection with Enhanced Glassmorphism
                        ConnectedLanguageSelectionView(
                            selectedLanguage: $userPreferences.selectedLanguage,
                            onLanguageSelected: { language in
                                userPreferences.updateLanguage(language)
                                // Trigger dashboard refresh
                                Task {
                                    await dashboardCoordinator.refreshDashboard()
                                }
                            }
                        )

                        // Language Portfolio Stats
                        LanguageStatsView()

                        // Quick Actions with Floating Glass Cards
                        ConnectedQuickActionsView(
                            selectedLanguage: userPreferences.selectedLanguage,
                            onActionSelected: { action in
                                handleQuickAction(action)
                            }
                        )

                        // Today's Recommendations with Emoji Reactions
                        ConnectedRecommendationsView(
                            recommendations: dashboardCoordinator.recommendations,
                            language: userPreferences.selectedLanguage
                        )

                        // Recent Activity with Micro-interactions
                        ConnectedRecentActivityView(
                            activities: dashboardCoordinator.recentActivities
                        )

                        // Achievement Showcase for Gen Z/Alpha
                        ConnectedAchievementShowcaseView(
                            achievements: dashboardCoordinator.achievements
                        )

                        // Quick Access Section
                        quickAccessSection
                    }
                    .padding()
                }

                // Celebration overlay - REMOVED TO STOP POPUP
                // if showingCelebration {
                //     CelebrationOverlayView()
                //         .transition(.opacity)
                //         .animation(.easeInOut(duration: 0.5), value: showingCelebration)
                // }
            }
            .navigationTitle("")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {}) {
                        Image(systemName: "bell")
                            .foregroundColor(.primaryText)
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {}) {
                        Image(systemName: "person.circle")
                            .foregroundColor(.primaryText)
                    }
                }
            }
            .onAppear {
                // REMOVED checkForMilestones() to stop popup
                // checkForMilestones()
                Task {
                    await dashboardCoordinator.refreshDashboard()
                }
            }
        }
    }

    // MARK: - Action Handlers

    private func handleQuickAction(_ action: QuickActionType) {
        switch action {
        case .startLesson:
            selectedTab = 1 // Navigate to Lessons tab
        case .practiceSpeak:
            // TODO: Navigate to AI conversation with selected language
            break
        case .culturalSim:
            selectedTab = 2 // Navigate to Simulations tab
        case .reviewWords:
            // TODO: Navigate to vocabulary review
            break
        }
    }

    private var dynamicBackgroundGradient: some View {
        let hour = Calendar.current.component(.hour, from: Date())
        let gradientColors: [Color]

        switch hour {
        case 5..<12:  // Morning
            gradientColors = Color.swissGradient
        case 12..<17: // Afternoon
            gradientColors = Color.mediterraneanGradient
        case 17..<20: // Evening
            gradientColors = Color.tuscanGradient
        default:      // Night
            gradientColors = Color.alpineGradient
        }

        return LinearGradient(
            colors: gradientColors.map { $0.opacity(colorScheme == .dark ? 0.4 : 0.2) },
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    private var quickAccessSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Start Learning")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primaryText)

            HStack(spacing: 16) {
                // AI Chat Access
                VStack(spacing: 12) {
                    ZStack {
                        Circle()
                            .fill(Color.emojiPurple.gradient)
                            .frame(width: 60, height: 60)

                        Image(systemName: "person.2.fill")
                            .font(.title2)
                            .foregroundColor(.white)
                    }

                    VStack(spacing: 4) {
                        Text("AI Agents")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primaryText)

                        Text("for \(userPreferences.selectedLanguage.displayName)")
                            .font(.caption)
                            .foregroundColor(.secondaryText)
                    }
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.cardBackground)
                .cornerRadius(20)
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                .onTapGesture {
                    selectedTab = 3 // Switch to AI Agents tab
                }

                VStack(spacing: 12) {
                    ZStack {
                        Circle()
                            .fill(Color.emojiBlue.gradient)
                            .frame(width: 60, height: 60)

                        Image(systemName: "book.fill")
                            .font(.title2)
                            .foregroundColor(.white)
                    }

                    VStack(spacing: 4) {
                        Text("Lessons")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primaryText)

                        Text("in \(userPreferences.selectedLanguage.displayName)")
                            .font(.caption)
                            .foregroundColor(.secondaryText)
                    }
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.cardBackground)
                .cornerRadius(20)
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                .onTapGesture {
                    selectedTab = 1 // Switch to Lessons tab
                }

                VStack(spacing: 12) {
                    ZStack {
                        Circle()
                            .fill(Color.emojiGreen.gradient)
                            .frame(width: 60, height: 60)

                        Image(systemName: "globe")
                            .font(.title2)
                            .foregroundColor(.white)
                    }

                    VStack(spacing: 4) {
                        Text("Simulations")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primaryText)

                        Text("Cultural scenarios")
                            .font(.caption)
                            .foregroundColor(.secondaryText)
                    }
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.cardBackground)
                .cornerRadius(20)
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                .onTapGesture {
                    selectedTab = 2 // Switch to Simulations tab
                }
            }
        }
    }
}

// MARK: - Animated Welcome Header Component

struct GlassmorphicWelcomeHeaderView: View {
    let streak: Int
    let userName: String
    @Binding var showCelebration: Bool

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Welcome back, \(userName)! 👋")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primaryText)

            HStack {
                HStack(spacing: 8) {
                    Image(systemName: "flame.fill")
                        .foregroundColor(.orange)
                        .scaleEffect(showCelebration ? 1.2 : 1.0)
                        .animation(.easeInOut(duration: 0.6).repeatForever(autoreverses: true), value: showCelebration)

                    Text("\(streak) day streak")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.orange.opacity(0.2))
                .cornerRadius(20)

                Spacer()

                Text(currentTimeGreeting)
                    .font(.subheadline)
                    .foregroundColor(.secondaryText)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [.niraGradientStart.opacity(0.8), .niraGradientEnd.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        )
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(Color.white.opacity(0.3), lineWidth: 1)
        )
        .shadow(color: .niraGradientStart.opacity(0.3), radius: 10, x: 0, y: 5)
    }

    private var currentTimeGreeting: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12: return "Good morning! ☀️"
        case 12..<17: return "Good afternoon! 🌤️"
        case 17..<22: return "Good evening! 🌅"
        default: return "Good night! 🌙"
        }
    }
}

#Preview {
    HomeView(selectedTab: .constant(0))
}