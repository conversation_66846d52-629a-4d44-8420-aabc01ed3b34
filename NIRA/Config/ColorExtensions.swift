//
//  ColorExtensions.swift
//  NIRA
//
//  Created by NIRA Team on 27/05/2025.
//

import SwiftUI

// MARK: - NIRA Color Extensions

extension Color {
    // MARK: - Gradient Collections
    // Note: Using direct Asset Catalog references to avoid conflicts with auto-generated extensions
    static let primaryGradient = [Color("NiraPrimary"), Color("NiraSecondary")]
    static let accentGradient = [Color("NiraAccent"), Color("NiraPrimary")]
    static let successGradient = [Color("NiraSuccess"), Color("NiraSuccess").opacity(0.7)]
    static let cardGradient = [Color("NiraCard"), Color("NiraSurface")]

    // European-Inspired Premium Gradients
    static let alpineGradient = [Color(red: 0.2, green: 0.3, blue: 0.5), Color(red: 0.4, green: 0.5, blue: 0.7)]
    static let mediterraneanGradient = [Color(red: 0.1, green: 0.4, blue: 0.6), Color(red: 0.2, green: 0.6, blue: 0.8)]
    static let nordicGradient = [Color(red: 0.3, green: 0.4, blue: 0.5), Color(red: 0.5, green: 0.6, blue: 0.7)]
    static let tuscanGradient = [Color(red: 0.6, green: 0.4, blue: 0.3), Color(red: 0.7, green: 0.5, blue: 0.4)]
    static let parisianGradient = [Color(red: 0.4, green: 0.3, blue: 0.5), Color(red: 0.6, green: 0.4, blue: 0.6)]
    static let swissGradient = [Color(red: 0.2, green: 0.5, blue: 0.4), Color(red: 0.3, green: 0.6, blue: 0.5)]

    // Premium Authentication Gradients
    static let authPrimaryGradient = [Color(red: 0.15, green: 0.25, blue: 0.45), Color(red: 0.25, green: 0.35, blue: 0.55)]
    static let authAccentGradient = [Color(red: 0.3, green: 0.5, blue: 0.7), Color(red: 0.4, green: 0.6, blue: 0.8)]
    static let authSurfaceGradient = [Color.white.opacity(0.95), Color.white.opacity(0.85)]

    // Dark mode adaptive colors (using Asset Catalog references)
    static let cardBackground = Color("NiraCard")
    static let primaryText = Color("NiraPrimaryText")
    static let secondaryText = Color("NiraSecondaryText")

    // Glassmorphism colors
    static let glassBackground = Color("NiraOverlay")
    static let glassBorder = Color("NiraPrimaryText").opacity(0.1)

    // Emoji-inspired colors - More Refined
    static let emojiYellow = Color(red: 0.9, green: 0.8, blue: 0.3)
    static let emojiPink = Color(red: 0.9, green: 0.4, blue: 0.6)
    static let emojiBlue = Color(red: 0.3, green: 0.6, blue: 0.9)
    static let emojiGreen = Color(red: 0.3, green: 0.8, blue: 0.4)
    static let emojiPurple = Color(red: 0.6, green: 0.3, blue: 0.8)
    static let emojiOrange = Color(red: 0.9, green: 0.5, blue: 0.2)

    // MARK: - Gradient Start Colors (for AuthenticationView)
    static let niraGradientStart = Color("NiraPrimary")
    static let niraGradientEnd = Color("NiraSecondary")

    // MARK: - Language-specific Colors (if these exist in Asset Catalog, remove them)
    static let frenchColor = Color("FrenchColor")
    static let spanishColor = Color("SpanishColor")
    static let germanColor = Color("GermanColor")
    static let italianColor = Color("ItalianColor")
    static let portugueseColor = Color("PortugueseColor")
    static let englishColor = Color("EnglishColor")
    static let japaneseColor = Color("JapaneseColor")
    static let tamilColor = Color("TamilColor")
}

// MARK: - Premium Design System

extension Font {
    // European-inspired typography hierarchy
    static let displayLarge = Font.system(size: 57, weight: .light, design: .default)
    static let displayMedium = Font.system(size: 45, weight: .light, design: .default)
    static let displaySmall = Font.system(size: 36, weight: .regular, design: .default)

    static let headlineLarge = Font.system(size: 32, weight: .medium, design: .default)
    static let headlineMedium = Font.system(size: 28, weight: .medium, design: .default)
    static let headlineSmall = Font.system(size: 24, weight: .medium, design: .default)

    static let titleLarge = Font.system(size: 22, weight: .semibold, design: .default)
    static let titleMedium = Font.system(size: 16, weight: .semibold, design: .default)
    static let titleSmall = Font.system(size: 14, weight: .semibold, design: .default)

    static let bodyLarge = Font.system(size: 16, weight: .regular, design: .default)
    static let bodyMedium = Font.system(size: 14, weight: .regular, design: .default)
    static let bodySmall = Font.system(size: 12, weight: .regular, design: .default)

    static let labelLarge = Font.system(size: 14, weight: .medium, design: .default)
    static let labelMedium = Font.system(size: 12, weight: .medium, design: .default)
    static let labelSmall = Font.system(size: 11, weight: .medium, design: .default)
}

// MARK: - Premium Spacing System

struct Spacing {
    static let xs: CGFloat = 4
    static let sm: CGFloat = 8
    static let md: CGFloat = 16
    static let lg: CGFloat = 24
    static let xl: CGFloat = 32
    static let xxl: CGFloat = 48
    static let xxxl: CGFloat = 64
}

// MARK: - Premium Corner Radius System

struct CornerRadius {
    static let xs: CGFloat = 4
    static let sm: CGFloat = 8
    static let md: CGFloat = 12
    static let lg: CGFloat = 16
    static let xl: CGFloat = 24
    static let xxl: CGFloat = 32
}